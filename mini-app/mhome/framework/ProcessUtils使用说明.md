# ProcessUtils 使用说明

## 问题修正

### 原始问题
```java
// 错误的代码 - Arrays.asList() 返回固定大小的列表
List<String> list = Arrays.asList(new String[]{javaBin, "-cp", classpath, className});
if(args!=null) list.addAll(Arrays.asList(args)); // 这里会抛出 UnsupportedOperationException
```

### 修正方案

#### 方案1：使用 ArrayList（推荐）
```java
// 创建可变的列表
List<String> list = new ArrayList<>();
list.add(javaBin);
list.add("-cp");
list.add(classpath);
list.add(className);

// 安全地添加额外参数
if (args != null && args.length > 0) {
    list.addAll(Arrays.asList(args));
}
```

#### 方案2：使用 ArrayList 构造函数
```java
// 基于现有数组创建可变列表
List<String> list = new ArrayList<>(Arrays.asList(javaBin, "-cp", classpath, className));
if (args != null && args.length > 0) {
    list.addAll(Arrays.asList(args));
}
```

#### 方案3：直接计算数组大小
```java
// 预先计算总大小
int totalSize = 4 + (args != null ? args.length : 0);
String[] command = new String[totalSize];
command[0] = javaBin;
command[1] = "-cp";
command[2] = classpath;
command[3] = className;

// 复制额外参数
if (args != null) {
    System.arraycopy(args, 0, command, 4, args.length);
}
```

## 使用示例

### 1. 基本重启
```java
// 重启当前程序，不传递额外参数
Process process = ProcessUtils.restart(MyApp.class);
```

### 2. 带程序参数重启
```java
// 重启程序并传递命令行参数
Process process = ProcessUtils.restartWithExtraArgs(
    MyApp.class,
    "--host", "localhost",
    "--port", "8080",
    "--verbose",
    "input.txt", "output.txt"
);
```

### 3. 带JVM参数重启
```java
// 重启程序并设置JVM参数和程序参数
String[] jvmArgs = {"-Xmx2g", "-Dfile.encoding=UTF-8"};
String[] programArgs = {"--config", "/path/to/config", "--debug"};

Process process = ProcessUtils.restartWithJvmArgs(
    MyApp.class,
    jvmArgs,
    programArgs
);
```

### 4. 结合命令行解析器
```java
// 使用动态命令行解析器的结果重启
DynamicArgs parsedArgs = DynamicArgsParser.parse(originalArgs);

// 构建新的参数数组
List<String> newArgs = new ArrayList<>();
for (String option : parsedArgs.getAllOptionNames()) {
    Object value = parsedArgs.getValue(option);
    if (parsedArgs.isFlag(option)) {
        newArgs.add("--" + option);
    } else {
        newArgs.add("--" + option);
        newArgs.add(value.toString());
    }
}
newArgs.addAll(parsedArgs.getPositionalArgs());

Process process = ProcessUtils.restartWithExtraArgs(
    MyApp.class,
    newArgs.toArray(new String[0])
);
```

## 实际应用场景

### 1. 配置文件更新后重启
```java
public void onConfigFileChanged(String newConfigPath) {
    System.out.println("配置文件已更新，重启程序...");
    ProcessUtils.restartWithExtraArgs(
        MyApp.class,
        "--config", newConfigPath,
        "--restart-reason", "config-updated"
    );
    System.exit(0);
}
```

### 2. 内存不足时重启并增加内存
```java
public void onOutOfMemoryError() {
    System.out.println("内存不足，重启程序并增加内存...");
    String[] jvmArgs = {"-Xmx4g", "-XX:+UseG1GC"};
    String[] programArgs = {"--restart-reason", "out-of-memory"};
    
    ProcessUtils.restartWithJvmArgs(MyApp.class, jvmArgs, programArgs);
    System.exit(1);
}
```

### 3. 自动更新后重启
```java
public void afterUpdate(String[] originalArgs) {
    System.out.println("程序已更新，重启应用新版本...");
    
    // 保持原有的命令行参数
    List<String> args = new ArrayList<>(Arrays.asList(originalArgs));
    args.add("--restart-reason");
    args.add("update-completed");
    
    ProcessUtils.restartWithExtraArgs(
        MyApp.class,
        args.toArray(new String[0])
    );
    System.exit(0);
}
```

## 最佳实践

### 1. 参数验证
```java
public static Process restartWithExtraArgs(Class<?> mainClass, String... args) {
    if (mainClass == null) {
        throw new IllegalArgumentException("mainClass 不能为 null");
    }
    
    // 验证Java环境
    String javaHome = System.getProperty("java.home");
    if (javaHome == null || javaHome.isEmpty()) {
        throw new RuntimeException("无法获取 java.home 系统属性");
    }
    
    // ... 其他实现
}
```

### 2. 日志记录
```java
public static Process restartWithExtraArgs(Class<?> mainClass, String... args) {
    // ... 构建命令
    
    System.out.println("重启命令: " + list);
    System.out.println("工作目录: " + System.getProperty("user.dir"));
    System.out.println("类路径: " + System.getProperty("java.class.path"));
    
    return start(list.toArray(new String[0]));
}
```

### 3. 错误处理
```java
public static Process restartWithExtraArgs(Class<?> mainClass, String... args) {
    try {
        // ... 构建和启动进程
        Process process = start(command);
        
        // 验证进程是否成功启动
        if (!process.isAlive()) {
            throw new RuntimeException("进程启动失败");
        }
        
        return process;
    } catch (Exception e) {
        System.err.println("重启失败: " + e.getMessage());
        throw new RuntimeException("程序重启失败", e);
    }
}
```

### 4. 优雅关闭
```java
public static void restartGracefully(Class<?> mainClass, String... args) {
    // 添加关闭钩子
    Runtime.getRuntime().addShutdownHook(new Thread(() -> {
        System.out.println("正在优雅关闭当前进程...");
        // 执行清理工作
    }));
    
    // 启动新进程
    Process newProcess = restartWithExtraArgs(mainClass, args);
    
    // 等待新进程启动成功
    try {
        Thread.sleep(2000);
        if (newProcess.isAlive()) {
            System.out.println("新进程启动成功，当前进程即将退出");
            System.exit(0);
        } else {
            System.err.println("新进程启动失败");
        }
    } catch (InterruptedException e) {
        Thread.currentThread().interrupt();
    }
}
```

## 注意事项

1. **Arrays.asList() 限制**：返回的是固定大小的列表，不能添加或删除元素
2. **进程生命周期**：确保新进程启动成功后再退出当前进程
3. **资源清理**：在退出前确保所有资源都已正确释放
4. **参数传递**：注意参数中的特殊字符和空格处理
5. **权限问题**：确保有足够的权限启动新进程
6. **类路径**：确保新进程能找到所有必需的类和资源

## 测试方法

```bash
# 编译测试类
javac -cp . ProcessUtilsTest.java

# 运行测试（无参数，会演示重启）
java ProcessUtilsTest

# 运行测试（带参数）
java ProcessUtilsTest --host localhost --port 8080 --verbose file1.txt file2.txt
```

通过这些修正和最佳实践，可以确保程序重启功能的稳定性和可靠性。
