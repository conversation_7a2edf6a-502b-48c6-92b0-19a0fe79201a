# JVM参数动态获取使用说明

## 概述

通过 `ManagementFactory.getRuntimeMXBean().getInputArguments()` 可以动态获取当前JVM的启动参数，这对于程序重启、参数分析、动态调优等场景非常有用。

## 基本用法

### 1. 获取当前JVM参数

```java
import java.lang.management.ManagementFactory;
import java.lang.management.RuntimeMXBean;
import java.util.List;

// 获取所有JVM启动参数
RuntimeMXBean runtimeMXBean = ManagementFactory.getRuntimeMXBean();
List<String> jvmArgs = runtimeMXBean.getInputArguments();

System.out.println("JVM参数:");
for (String arg : jvmArgs) {
    System.out.println("  " + arg);
}
```

### 2. 使用ProcessUtils获取参数

```java
// 获取所有JVM参数
List<String> allArgs = ProcessUtils.getCurrentJvmArgs();

// 获取过滤后的JVM参数（排除调试、代理等参数）
List<String> filteredArgs = ProcessUtils.getFilteredJvmArgs();

// 获取详细的JVM信息
String jvmInfo = ProcessUtils.getJvmInfo();
System.out.println(jvmInfo);
```

## 高级用法

### 1. 使用当前JVM参数重启程序

```java
// 保持当前JVM参数重启
Process process = ProcessUtils.restartWithCurrentJvmArgs(
    MyApp.class,
    "--config", "new-config.properties"
);
```

### 2. 解析和修改JVM参数

```java
// 获取当前JVM参数
List<String> currentArgs = ProcessUtils.getCurrentJvmArgs();

// 解析为结构化对象
JvmArgsUtils.JvmArgsInfo argsInfo = JvmArgsUtils.parseJvmArgs(currentArgs);

// 修改参数
JvmArgsUtils.JvmArgsInfo modifiedArgs = JvmArgsUtils.modifyJvmArgs(argsInfo,
    JvmArgsUtils.CommonModifiers.combine(
        JvmArgsUtils.CommonModifiers.setHeapSize("512m", "2g"),
        JvmArgsUtils.CommonModifiers.setGC("G1GC"),
        JvmArgsUtils.CommonModifiers.addSystemProperty("file.encoding", "UTF-8")
    )
);

// 构建新的JVM参数列表
List<String> newJvmArgs = JvmArgsUtils.buildJvmArgs(modifiedArgs);

// 使用新参数重启
ProcessUtils.restartWithJvmArgs(MyApp.class, 
    newJvmArgs.toArray(new String[0]), 
    new String[]{"--restarted", "true"});
```

## 实际应用场景

### 1. 内存不足时自动增加内存重启

```java
public class MemoryMonitor {
    public static void checkMemoryAndRestart() {
        Runtime runtime = Runtime.getRuntime();
        long usedMemory = runtime.totalMemory() - runtime.freeMemory();
        long maxMemory = runtime.maxMemory();
        
        double usagePercent = (double) usedMemory / maxMemory * 100;
        
        if (usagePercent > 90) {
            System.out.println("内存使用过高，增加内存重启...");
            
            // 获取当前JVM参数并增加内存
            List<String> currentArgs = ProcessUtils.getFilteredJvmArgs();
            JvmArgsUtils.JvmArgsInfo argsInfo = JvmArgsUtils.parseJvmArgs(currentArgs);
            
            // 将最大堆内存翻倍
            JvmArgsUtils.JvmArgsInfo newArgs = JvmArgsUtils.modifyJvmArgs(argsInfo,
                JvmArgsUtils.CommonModifiers.increaseHeapSize(2.0)
            );
            
            List<String> newJvmArgs = JvmArgsUtils.buildJvmArgs(newArgs);
            ProcessUtils.restartWithJvmArgs(MyApp.class, 
                newJvmArgs.toArray(new String[0]), 
                new String[]{"--restart-reason", "memory-upgrade"});
            
            System.exit(0);
        }
    }
}
```

### 2. 配置更新后保持JVM参数重启

```java
public void onConfigurationChanged(String newConfigPath) {
    System.out.println("配置已更新，重启程序...");
    
    // 使用当前JVM参数重启，只改变程序参数
    ProcessUtils.restartWithCurrentJvmArgs(
        MyApp.class,
        "--config", newConfigPath,
        "--restart-reason", "config-updated",
        "--timestamp", String.valueOf(System.currentTimeMillis())
    );
    
    System.exit(0);
}
```

### 3. 生产环境优化重启

```java
public void optimizeForProduction() {
    List<String> currentArgs = ProcessUtils.getCurrentJvmArgs();
    JvmArgsUtils.JvmArgsInfo argsInfo = JvmArgsUtils.parseJvmArgs(currentArgs);
    
    // 生产环境优化
    JvmArgsUtils.JvmArgsInfo optimizedArgs = JvmArgsUtils.modifyJvmArgs(argsInfo,
        JvmArgsUtils.CommonModifiers.combine(
            // 设置合适的堆内存
            JvmArgsUtils.CommonModifiers.setHeapSize("1g", "4g"),
            // 使用G1GC
            JvmArgsUtils.CommonModifiers.setGC("G1GC"),
            // 移除调试选项
            JvmArgsUtils.CommonModifiers.removeDebugOptions(),
            // 添加生产环境系统属性
            JvmArgsUtils.CommonModifiers.addSystemProperty("spring.profiles.active", "prod"),
            JvmArgsUtils.CommonModifiers.addSystemProperty("file.encoding", "UTF-8")
        )
    );
    
    List<String> newJvmArgs = JvmArgsUtils.buildJvmArgs(optimizedArgs);
    ProcessUtils.restartWithJvmArgs(MyApp.class, 
        newJvmArgs.toArray(new String[0]), 
        new String[]{"--mode", "production"});
}
```

### 4. 动态JVM调优

```java
public class JvmTuner {
    public static void tuneForHighThroughput() {
        // 高吞吐量调优
        JvmArgsUtils.JvmArgsModifier throughputModifier = args -> {
            // 使用Parallel GC
            args.xxOptions.removeIf(opt -> opt.contains("GC"));
            args.xxOptions.add("-XX:+UseParallelGC");
            args.xxOptions.add("-XX:+UseParallelOldGC");
            
            // 调整GC参数
            args.xxOptions.add("-XX:MaxGCPauseMillis=200");
            args.xxOptions.add("-XX:GCTimeRatio=19");
            
            // 增加堆内存
            if (args.maxHeapSize != null) {
                long currentBytes = JvmArgsUtils.parseMemorySize(args.maxHeapSize);
                args.maxHeapSize = JvmArgsUtils.formatMemorySize(currentBytes * 2);
            }
        };
        
        restartWithModifier(throughputModifier, "--tuning", "throughput");
    }
    
    public static void tuneForLowLatency() {
        // 低延迟调优
        JvmArgsUtils.JvmArgsModifier latencyModifier = args -> {
            // 使用G1GC
            args.xxOptions.removeIf(opt -> opt.contains("GC"));
            args.xxOptions.add("-XX:+UseG1GC");
            args.xxOptions.add("-XX:MaxGCPauseMillis=50");
            args.xxOptions.add("-XX:G1HeapRegionSize=16m");
        };
        
        restartWithModifier(latencyModifier, "--tuning", "latency");
    }
    
    private static void restartWithModifier(JvmArgsUtils.JvmArgsModifier modifier, String... programArgs) {
        List<String> currentArgs = ProcessUtils.getFilteredJvmArgs();
        JvmArgsUtils.JvmArgsInfo argsInfo = JvmArgsUtils.parseJvmArgs(currentArgs);
        JvmArgsUtils.JvmArgsInfo tunedArgs = JvmArgsUtils.modifyJvmArgs(argsInfo, modifier);
        
        List<String> newJvmArgs = JvmArgsUtils.buildJvmArgs(tunedArgs);
        ProcessUtils.restartWithJvmArgs(JvmTuner.class, 
            newJvmArgs.toArray(new String[0]), programArgs);
    }
}
```

## 常见JVM参数说明

### 内存参数
- `-Xms<size>`: 初始堆内存大小
- `-Xmx<size>`: 最大堆内存大小
- `-XX:NewRatio=<ratio>`: 老年代与新生代的比例
- `-XX:MaxMetaspaceSize=<size>`: 元空间最大大小

### GC参数
- `-XX:+UseG1GC`: 使用G1垃圾收集器
- `-XX:+UseParallelGC`: 使用并行垃圾收集器
- `-XX:+UseConcMarkSweepGC`: 使用CMS垃圾收集器
- `-XX:MaxGCPauseMillis=<time>`: 最大GC暂停时间

### 调试参数
- `-XX:+PrintGC`: 打印GC信息
- `-XX:+PrintGCDetails`: 打印详细GC信息
- `-Xloggc:<file>`: GC日志文件
- `-XX:+HeapDumpOnOutOfMemoryError`: OOM时生成堆转储

### 系统属性
- `-Dfile.encoding=UTF-8`: 文件编码
- `-Duser.timezone=Asia/Shanghai`: 时区设置
- `-Djava.awt.headless=true`: 无头模式

## 注意事项

1. **参数过滤**: 某些参数（如调试代理、JMX等）不适合传递给新进程
2. **权限问题**: 确保有足够权限启动新进程
3. **资源清理**: 重启前确保当前进程资源已正确释放
4. **参数验证**: 修改参数时要验证参数的有效性
5. **版本兼容**: 不同JVM版本支持的参数可能不同

## 测试方法

```bash
# 使用特定JVM参数启动程序
java -Xmx1g -XX:+UseG1GC -Dfile.encoding=UTF-8 JvmArgsDemo

# 查看JVM参数获取效果
java -Xms512m -Xmx2g -XX:+PrintGCDetails -Dprop=value JvmArgsDemo
```

通过这些功能，可以实现智能的程序重启和JVM参数动态调优。
