package com.mhome.env.args;

import java.util.HashMap;
import java.util.Map;

public class ArgParser {

    private final Map<String, OptionConfig> options = new HashMap<>();
    private String currentOption;

    public ArgParser option(String name) {
        currentOption = name;
        options.put(name, new OptionConfig());
        return this;
    }

    public ArgParser defaultValue(Object value) {
        if (currentOption != null) {
            options.get(currentOption).defaultValue = value;
        }
        return this;
    }

    public ArgParser asInt() {
        if (currentOption != null) {
            options.get(currentOption).type = Integer.class;
        }
        return this;
    }

    public ArgParser asFlag() {
        if (currentOption != null) {
            options.get(currentOption).isFlag = true;
        }
        return this;
    }

}
