package com.mhome;

import java.util.Arrays;

import com.mhome.env.ProcessUtils;
import com.mhome.env.args.DynamicArgsParser;

public class Main {
    public static void main(String[] args) {

        System.out.println(Arrays.asList(args));
        System.out.println("Hello world!");

        Runtime.getRuntime().addShutdownHook(new Thread(Main::test));

        int loop = DynamicArgsParser.parse().getInt("test");
        if(loop > 10) System.exit(0);
        System.out.println("loop = " + loop);
        loop = loop + 1;
        ProcessUtils.restartWithExtraArgs(Main.class,"--test=" + loop);
        System.exit(1);
        try{
        Thread.currentThread().join();
        }catch (InterruptedException e){
            e.printStackTrace();
        }
    }

    private static void test(){
        System.out.println("end test");
    }
}


