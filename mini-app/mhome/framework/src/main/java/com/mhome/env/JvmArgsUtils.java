package com.mhome.env;

import java.lang.management.ManagementFactory;
import java.lang.management.RuntimeMXBean;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * JVM参数工具类
 * 提供JVM参数的获取、解析、修改等功能
 */
public class JvmArgsUtils {
    
    private static final Pattern MEMORY_PATTERN = Pattern.compile("^(-Xm[sx])(.+)$");
    private static final Pattern SYSTEM_PROPERTY_PATTERN = Pattern.compile("^-D(.+?)=(.*)$");
    
    /**
     * 获取当前JVM的所有启动参数
     */
    public static List<String> getCurrentJvmArgs() {
        RuntimeMXBean runtimeMXBean = ManagementFactory.getRuntimeMXBean();
        return new ArrayList<>(runtimeMXBean.getInputArguments());
    }
    
    /**
     * 解析JVM参数为结构化对象
     */
    public static JvmArgsInfo parseJvmArgs(List<String> jvmArgs) {
        JvmArgsInfo info = new JvmArgsInfo();
        
        for (String arg : jvmArgs) {
            if (arg.startsWith("-Xmx")) {
                info.maxHeapSize = arg.substring(4);
            } else if (arg.startsWith("-Xms")) {
                info.initialHeapSize = arg.substring(4);
            } else if (arg.startsWith("-XX:")) {
                info.xxOptions.add(arg);
                parseXXOption(arg, info);
            } else if (arg.startsWith("-D")) {
                info.systemProperties.add(arg);
                parseSystemProperty(arg, info);
            } else if (arg.startsWith("-javaagent:")) {
                info.javaAgents.add(arg);
            } else if (arg.startsWith("-agentlib:")) {
                info.agentLibs.add(arg);
            } else {
                info.otherOptions.add(arg);
            }
        }
        
        return info;
    }
    
    private static void parseXXOption(String arg, JvmArgsInfo info) {
        if (arg.contains("GC")) {
            info.gcOptions.add(arg);
        } else if (arg.contains("Debug") || arg.contains("Print")) {
            info.debugOptions.add(arg);
        }
    }
    
    private static void parseSystemProperty(String arg, JvmArgsInfo info) {
        Matcher matcher = SYSTEM_PROPERTY_PATTERN.matcher(arg);
        if (matcher.matches()) {
            String key = matcher.group(1);
            String value = matcher.group(2);
            info.systemPropertiesMap.put(key, value);
        }
    }
    
    /**
     * 构建JVM参数列表
     */
    public static List<String> buildJvmArgs(JvmArgsInfo info) {
        List<String> args = new ArrayList<>();
        
        // 内存参数
        if (info.initialHeapSize != null) {
            args.add("-Xms" + info.initialHeapSize);
        }
        if (info.maxHeapSize != null) {
            args.add("-Xmx" + info.maxHeapSize);
        }
        
        // XX选项
        args.addAll(info.xxOptions);
        
        // 系统属性
        args.addAll(info.systemProperties);
        
        // Java代理
        args.addAll(info.javaAgents);
        args.addAll(info.agentLibs);
        
        // 其他选项
        args.addAll(info.otherOptions);
        
        return args;
    }
    
    /**
     * 修改JVM参数
     */
    public static JvmArgsInfo modifyJvmArgs(JvmArgsInfo original, JvmArgsModifier modifier) {
        JvmArgsInfo modified = original.copy();
        modifier.modify(modified);
        return modified;
    }
    
    /**
     * 解析内存大小字符串为字节数
     */
    public static long parseMemorySize(String memoryStr) {
        if (memoryStr == null || memoryStr.isEmpty()) {
            return 0;
        }
        
        String str = memoryStr.toLowerCase().trim();
        long multiplier = 1;
        
        if (str.endsWith("k")) {
            multiplier = 1024;
            str = str.substring(0, str.length() - 1);
        } else if (str.endsWith("m")) {
            multiplier = 1024 * 1024;
            str = str.substring(0, str.length() - 1);
        } else if (str.endsWith("g")) {
            multiplier = 1024 * 1024 * 1024;
            str = str.substring(0, str.length() - 1);
        }
        
        try {
            return Long.parseLong(str) * multiplier;
        } catch (NumberFormatException e) {
            return 0;
        }
    }
    
    /**
     * 格式化字节数为内存大小字符串
     */
    public static String formatMemorySize(long bytes) {
        if (bytes >= 1024 * 1024 * 1024) {
            return (bytes / (1024 * 1024 * 1024)) + "g";
        } else if (bytes >= 1024 * 1024) {
            return (bytes / (1024 * 1024)) + "m";
        } else if (bytes >= 1024) {
            return (bytes / 1024) + "k";
        } else {
            return String.valueOf(bytes);
        }
    }
    
    /**
     * JVM参数信息类
     */
    public static class JvmArgsInfo {
        public String initialHeapSize;
        public String maxHeapSize;
        public List<String> xxOptions = new ArrayList<>();
        public List<String> systemProperties = new ArrayList<>();
        public List<String> javaAgents = new ArrayList<>();
        public List<String> agentLibs = new ArrayList<>();
        public List<String> otherOptions = new ArrayList<>();
        
        // 分类存储
        public List<String> gcOptions = new ArrayList<>();
        public List<String> debugOptions = new ArrayList<>();
        public Map<String, String> systemPropertiesMap = new HashMap<>();
        
        public JvmArgsInfo copy() {
            JvmArgsInfo copy = new JvmArgsInfo();
            copy.initialHeapSize = this.initialHeapSize;
            copy.maxHeapSize = this.maxHeapSize;
            copy.xxOptions = new ArrayList<>(this.xxOptions);
            copy.systemProperties = new ArrayList<>(this.systemProperties);
            copy.javaAgents = new ArrayList<>(this.javaAgents);
            copy.agentLibs = new ArrayList<>(this.agentLibs);
            copy.otherOptions = new ArrayList<>(this.otherOptions);
            copy.gcOptions = new ArrayList<>(this.gcOptions);
            copy.debugOptions = new ArrayList<>(this.debugOptions);
            copy.systemPropertiesMap = new HashMap<>(this.systemPropertiesMap);
            return copy;
        }
        
        @Override
        public String toString() {
            return String.format("JvmArgsInfo{heap=%s-%s, xxOptions=%d, sysProps=%d, agents=%d}", 
                               initialHeapSize, maxHeapSize, xxOptions.size(), 
                               systemProperties.size(), javaAgents.size());
        }
    }
    
    /**
     * JVM参数修改器接口
     */
    @FunctionalInterface
    public interface JvmArgsModifier {
        void modify(JvmArgsInfo args);
    }
    
    /**
     * 常用的JVM参数修改器
     */
    public static class CommonModifiers {
        
        /**
         * 设置堆内存大小
         */
        public static JvmArgsModifier setHeapSize(String initialSize, String maxSize) {
            return args -> {
                if (initialSize != null) args.initialHeapSize = initialSize;
                if (maxSize != null) args.maxHeapSize = maxSize;
            };
        }
        
        /**
         * 增加堆内存
         */
        public static JvmArgsModifier increaseHeapSize(double factor) {
            return args -> {
                if (args.maxHeapSize != null) {
                    long currentBytes = parseMemorySize(args.maxHeapSize);
                    long newBytes = (long) (currentBytes * factor);
                    args.maxHeapSize = formatMemorySize(newBytes);
                }
                if (args.initialHeapSize != null) {
                    long currentBytes = parseMemorySize(args.initialHeapSize);
                    long newBytes = (long) (currentBytes * factor);
                    args.initialHeapSize = formatMemorySize(newBytes);
                }
            };
        }
        
        /**
         * 设置GC类型
         */
        public static JvmArgsModifier setGC(String gcType) {
            return args -> {
                // 移除现有的GC选项
                args.xxOptions.removeIf(opt -> opt.contains("GC"));
                args.gcOptions.clear();
                
                // 添加新的GC选项
                String gcOption = "-XX:+Use" + gcType;
                args.xxOptions.add(gcOption);
                args.gcOptions.add(gcOption);
            };
        }
        
        /**
         * 添加系统属性
         */
        public static JvmArgsModifier addSystemProperty(String key, String value) {
            return args -> {
                String prop = "-D" + key + "=" + value;
                args.systemProperties.add(prop);
                args.systemPropertiesMap.put(key, value);
            };
        }
        
        /**
         * 移除调试选项
         */
        public static JvmArgsModifier removeDebugOptions() {
            return args -> {
                args.xxOptions.removeIf(opt -> 
                    opt.contains("Debug") || opt.contains("Print") || 
                    opt.contains("jdwp") || opt.contains("agentlib"));
                args.debugOptions.clear();
                args.agentLibs.clear();
            };
        }
        
        /**
         * 组合多个修改器
         */
        public static JvmArgsModifier combine(JvmArgsModifier... modifiers) {
            return args -> {
                for (JvmArgsModifier modifier : modifiers) {
                    modifier.modify(args);
                }
            };
        }
    }
    
    /**
     * 获取当前JVM参数的摘要信息
     */
    public static String getJvmArgsSummary() {
        List<String> jvmArgs = getCurrentJvmArgs();
        JvmArgsInfo info = parseJvmArgs(jvmArgs);
        
        StringBuilder summary = new StringBuilder();
        summary.append("JVM参数摘要:\n");
        summary.append("  堆内存: ").append(info.initialHeapSize).append(" - ").append(info.maxHeapSize).append("\n");
        summary.append("  GC选项: ").append(info.gcOptions.size()).append(" 个\n");
        summary.append("  系统属性: ").append(info.systemPropertiesMap.size()).append(" 个\n");
        summary.append("  Java代理: ").append(info.javaAgents.size()).append(" 个\n");
        summary.append("  调试选项: ").append(info.debugOptions.size()).append(" 个\n");
        summary.append("  总参数数: ").append(jvmArgs.size()).append(" 个\n");
        
        return summary.toString();
    }
}
