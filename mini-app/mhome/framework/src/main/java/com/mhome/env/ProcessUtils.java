package com.mhome.env;

import java.io.File;
import java.lang.management.ManagementFactory;
import java.lang.management.RuntimeMXBean;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class ProcessUtils {

    public static Process start(String[] cmd){
        try {
            ProcessBuilder pb = new ProcessBuilder(cmd);
            pb.inheritIO();
            return pb.start();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    public static Process start(String[] cmd,File dir){
        try {
            ProcessBuilder pb = new ProcessBuilder(cmd);
            pb.directory(dir);
            pb.inheritIO();
            return pb.start();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static Process restart(Class<?> mainClass){
        String javaHome = System.getProperty("java.home");
        String javaBin = javaHome + File.separator + "bin" + File.separator + "java";
        String classpath = System.getProperty("java.class.path");
        String className = mainClass.getName();

        return start(new String[]{javaBin, "-cp", classpath, className});
    }

    public static Process restartWithExtraArgs(Class<?> mainClass, String... args) {
        String javaHome = System.getProperty("java.home");
        String javaBin = javaHome + File.separator + "bin" + File.separator + "java";
        String classpath = System.getProperty("java.class.path");
        String className = mainClass.getName();

        // 创建可变的列表
        List<String> list = new ArrayList<String>(){{add(javaBin);add("-cp");add(classpath);add(className);}};
        
        System.out.println("基础命令: " + list);

        // 添加额外参数
        if (args != null && args.length > 0) {
            list.addAll(Arrays.asList(args));
            System.out.println("添加参数: " + Arrays.asList(args));
        }

        System.out.println("最终命令: " + list);
        return start(list.toArray(new String[0]));
    }

    /**
     * 重启当前程序并传递JVM参数和程序参数
     * @param mainClass 主类
     * @param jvmArgs JVM参数（如 -Xmx1g, -Dfile.encoding=UTF-8）
     * @param programArgs 程序参数
     * @return Process对象
     */
    public static Process restartWithJvmArgs(Class<?> mainClass, String[] jvmArgs, String[] programArgs) {
        String javaHome = System.getProperty("java.home");
        String javaBin = javaHome + File.separator + "bin" + File.separator + "java";
        String classpath = System.getProperty("java.class.path");
        String className = mainClass.getName();

        List<String> list = new ArrayList<>();
        list.add(javaBin);

        // 添加JVM参数
        if (jvmArgs != null && jvmArgs.length > 0) {
            list.addAll(Arrays.asList(jvmArgs));
        }

        list.add("-cp");
        list.add(classpath);
        list.add(className);

        // 添加程序参数
        if (programArgs != null && programArgs.length > 0) {
            list.addAll(Arrays.asList(programArgs));
        }

        System.out.println("重启命令: " + list);
        return start(list.toArray(new String[0]));
    }

    /**
     * 使用命令行参数解析器重启程序
     * @param mainClass 主类
     * @param args 解析后的参数对象
     * @return Process对象
     */
    public static Process restartWithParsedArgs(Class<?> mainClass, Object args) {
        // 这里可以根据具体的参数解析器类型来处理
        // 暂时使用toString()方法，实际使用时可以根据需要扩展
        return restartWithExtraArgs(mainClass, args.toString());
    }

    /**
     * 获取当前JVM的启动参数
     * @return JVM参数列表
     */
    public static List<String> getCurrentJvmArgs() {
        RuntimeMXBean runtimeMXBean = ManagementFactory.getRuntimeMXBean();
        return new ArrayList<>(runtimeMXBean.getInputArguments());
    }

    /**
     * 获取过滤后的JVM参数（排除一些不适合传递的参数）
     * @return 过滤后的JVM参数列表
     */
    public static List<String> getFilteredJvmArgs() {
        List<String> allArgs = getCurrentJvmArgs();
        List<String> filteredArgs = new ArrayList<>();

        for (String arg : allArgs) {
            // 过滤掉一些不适合传递给新进程的参数
            if (shouldIncludeJvmArg(arg)) {
                filteredArgs.add(arg);
            }
        }

        return filteredArgs;
    }

    /**
     * 判断JVM参数是否应该包含在重启时
     * @param arg JVM参数
     * @return 是否包含
     */
    private static boolean shouldIncludeJvmArg(String arg) {
        // 排除一些可能导致问题的参数
        String[] excludePatterns = {
            "-agentlib:",           // 调试代理
            "-javaagent:",          // Java代理
            "-XX:+PrintGC",         // GC日志相关
            "-XX:+PrintGCDetails",
            "-Xloggc:",
            "-XX:+HeapDumpOnOutOfMemoryError", // 可能导致路径问题
            "-XX:HeapDumpPath=",
            "-Dcom.sun.management.jmxremote", // JMX远程管理
            "-Djava.awt.headless=true" // 某些情况下可能需要重新设置
        };

        for (String pattern : excludePatterns) {
            if (arg.startsWith(pattern)) {
                return false;
            }
        }

        return true;
    }

    /**
     * 使用当前JVM参数重启程序
     * @param mainClass 主类
     * @param programArgs 程序参数
     * @return Process对象
     */
    public static Process restartWithCurrentJvmArgs(Class<?> mainClass, String... programArgs) {
        List<String> jvmArgs = getFilteredJvmArgs();
        return restartWithJvmArgs(mainClass, jvmArgs.toArray(new String[0]), programArgs);
    }

    /**
     * 获取JVM信息的详细报告
     * @return JVM信息字符串
     */
    public static String getJvmInfo() {
        RuntimeMXBean runtimeMXBean = ManagementFactory.getRuntimeMXBean();
        StringBuilder info = new StringBuilder();

        info.append("=== JVM 信息 ===\n");
        info.append("JVM名称: ").append(runtimeMXBean.getVmName()).append("\n");
        info.append("JVM版本: ").append(runtimeMXBean.getVmVersion()).append("\n");
        info.append("JVM供应商: ").append(runtimeMXBean.getVmVendor()).append("\n");
        info.append("启动时间: ").append(new java.util.Date(runtimeMXBean.getStartTime())).append("\n");
        info.append("运行时间: ").append(runtimeMXBean.getUptime()).append(" ms\n");
        info.append("进程ID: ").append(runtimeMXBean.getName().split("@")[0]).append("\n");

        info.append("\n=== JVM 启动参数 ===\n");
        List<String> jvmArgs = runtimeMXBean.getInputArguments();
        if (jvmArgs.isEmpty()) {
            info.append("无JVM参数\n");
        } else {
            for (int i = 0; i < jvmArgs.size(); i++) {
                info.append(String.format("[%d] %s\n", i, jvmArgs.get(i)));
            }
        }

        info.append("\n=== 系统属性 ===\n");
        info.append("Java版本: ").append(System.getProperty("java.version")).append("\n");
        info.append("Java主目录: ").append(System.getProperty("java.home")).append("\n");
        info.append("类路径: ").append(System.getProperty("java.class.path")).append("\n");
        info.append("工作目录: ").append(System.getProperty("user.dir")).append("\n");
        info.append("用户名: ").append(System.getProperty("user.name")).append("\n");
        info.append("操作系统: ").append(System.getProperty("os.name")).append(" ")
            .append(System.getProperty("os.version")).append("\n");

        return info.toString();
    }


}
