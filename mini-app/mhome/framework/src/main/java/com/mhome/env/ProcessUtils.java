package com.mhome.env;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class ProcessUtils {

    public static Process start(String[] cmd){
        try {
            ProcessBuilder pb = new ProcessBuilder(cmd);
            pb.inheritIO();
            return pb.start();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    public static Process start(String[] cmd,File dir){
        try {
            ProcessBuilder pb = new ProcessBuilder(cmd);
            pb.directory(dir);
            pb.inheritIO();
            return pb.start();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static Process restart(Class<?> mainClass){
        String javaHome = System.getProperty("java.home");
        String javaBin = javaHome + File.separator + "bin" + File.separator + "java";
        String classpath = System.getProperty("java.class.path");
        String className = mainClass.getName();

        return start(new String[]{javaBin, "-cp", classpath, className});
    }

    public static Process restartWithExtraArgs(Class<?> mainClass, String... args) {
        String javaHome = System.getProperty("java.home");
        String javaBin = javaHome + File.separator + "bin" + File.separator + "java";
        String classpath = System.getProperty("java.class.path");
        String className = mainClass.getName();

        // 创建可变的列表
        List<String> list = new ArrayList<>();
        list.add(javaBin);
        list.add("-cp");
        list.add(classpath);
        list.add(className);

        System.out.println("基础命令: " + list);

        // 添加额外参数
        if (args != null && args.length > 0) {
            list.addAll(Arrays.asList(args));
            System.out.println("添加参数: " + Arrays.asList(args));
        }

        System.out.println("最终命令: " + list);
        return start(list.toArray(new String[0]));
    }

    /**
     * 重启当前程序并传递JVM参数和程序参数
     * @param mainClass 主类
     * @param jvmArgs JVM参数（如 -Xmx1g, -Dfile.encoding=UTF-8）
     * @param programArgs 程序参数
     * @return Process对象
     */
    public static Process restartWithJvmArgs(Class<?> mainClass, String[] jvmArgs, String[] programArgs) {
        String javaHome = System.getProperty("java.home");
        String javaBin = javaHome + File.separator + "bin" + File.separator + "java";
        String classpath = System.getProperty("java.class.path");
        String className = mainClass.getName();

        List<String> list = new ArrayList<>();
        list.add(javaBin);

        // 添加JVM参数
        if (jvmArgs != null && jvmArgs.length > 0) {
            list.addAll(Arrays.asList(jvmArgs));
        }

        list.add("-cp");
        list.add(classpath);
        list.add(className);

        // 添加程序参数
        if (programArgs != null && programArgs.length > 0) {
            list.addAll(Arrays.asList(programArgs));
        }

        System.out.println("重启命令: " + list);
        return start(list.toArray(new String[0]));
    }

    /**
     * 使用命令行参数解析器重启程序
     * @param mainClass 主类
     * @param args 解析后的参数对象
     * @return Process对象
     */
    public static Process restartWithParsedArgs(Class<?> mainClass, Object args) {
        // 这里可以根据具体的参数解析器类型来处理
        // 暂时使用toString()方法，实际使用时可以根据需要扩展
        return restartWithExtraArgs(mainClass, args.toString());
    }


}
