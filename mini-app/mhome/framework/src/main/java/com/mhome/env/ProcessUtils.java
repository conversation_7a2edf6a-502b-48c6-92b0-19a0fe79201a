package com.mhome.env;

import java.io.File;
import java.lang.management.ManagementFactory;
import java.lang.management.RuntimeMXBean;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;

public class ProcessUtils {

    // 存储程序启动参数
    private static String[] originalProgramArgs = new String[0];
    private static final Map<String, String> programArgsCache = new ConcurrentHashMap<>();
    private static boolean argsCached = false;

    public static Process start(String[] cmd){
        try {
            ProcessBuilder pb = new ProcessBuilder(cmd);
            pb.inheritIO();
            return pb.start();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    public static Process start(String[] cmd,File dir){
        try {
            ProcessBuilder pb = new ProcessBuilder(cmd);
            pb.directory(dir);
            pb.inheritIO();
            return pb.start();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static Process restart(Class<?> mainClass){
        String javaHome = System.getProperty("java.home");
        String javaBin = javaHome + File.separator + "bin" + File.separator + "java";
        String classpath = System.getProperty("java.class.path");
        String className = mainClass.getName();

        return start(new String[]{javaBin, "-cp", classpath, className});
    }

    public static Process restartWithExtraArgs(Class<?> mainClass, String... args) {
        String javaHome = System.getProperty("java.home");
        String javaBin = javaHome + File.separator + "bin" + File.separator + "java";
        String classpath = System.getProperty("java.class.path");
        String className = mainClass.getName();

        // 创建可变的列表
        List<String> list = new ArrayList<String>(){{add(javaBin);add("-cp");add(classpath);add(className);}};
        
        System.out.println("基础命令: " + list);

        // 添加额外参数
        if (args != null && args.length > 0) {
            list.addAll(Arrays.asList(args));
            System.out.println("添加参数: " + Arrays.asList(args));
        }

        System.out.println("最终命令: " + list);
        return start(list.toArray(new String[0]));
    }

    /**
     * 重启当前程序并传递JVM参数和程序参数
     * @param mainClass 主类
     * @param jvmArgs JVM参数（如 -Xmx1g, -Dfile.encoding=UTF-8）
     * @param programArgs 程序参数
     * @return Process对象
     */
    public static Process restartWithJvmArgs(Class<?> mainClass, String[] jvmArgs, String[] programArgs) {
        String javaHome = System.getProperty("java.home");
        String javaBin = javaHome + File.separator + "bin" + File.separator + "java";
        String classpath = System.getProperty("java.class.path");
        String className = mainClass.getName();

        List<String> list = new ArrayList<>();
        list.add(javaBin);

        // 添加JVM参数
        if (jvmArgs != null && jvmArgs.length > 0) {
            list.addAll(Arrays.asList(jvmArgs));
        }

        list.add("-cp");
        list.add(classpath);
        list.add(className);

        // 添加程序参数
        if (programArgs != null && programArgs.length > 0) {
            list.addAll(Arrays.asList(programArgs));
        }

        System.out.println("重启命令: " + list);
        return start(list.toArray(new String[0]));
    }

    /**
     * 使用命令行参数解析器重启程序
     * @param mainClass 主类
     * @param args 解析后的参数对象
     * @return Process对象
     */
    public static Process restartWithParsedArgs(Class<?> mainClass, Object args) {
        // 这里可以根据具体的参数解析器类型来处理
        // 暂时使用toString()方法，实际使用时可以根据需要扩展
        return restartWithExtraArgs(mainClass, args.toString());
    }

    /**
     * 获取当前JVM的启动参数
     * @return JVM参数列表
     */
    public static List<String> getCurrentJvmArgs() {
        RuntimeMXBean runtimeMXBean = ManagementFactory.getRuntimeMXBean();
        return new ArrayList<>(runtimeMXBean.getInputArguments());
    }

    /**
     * 获取过滤后的JVM参数（排除一些不适合传递的参数）
     * @return 过滤后的JVM参数列表
     */
    public static List<String> getFilteredJvmArgs() {
        List<String> allArgs = getCurrentJvmArgs();
        List<String> filteredArgs = new ArrayList<>();

        for (String arg : allArgs) {
            // 过滤掉一些不适合传递给新进程的参数
            if (shouldIncludeJvmArg(arg)) {
                filteredArgs.add(arg);
            }
        }

        return filteredArgs;
    }

    /**
     * 判断JVM参数是否应该包含在重启时
     * @param arg JVM参数
     * @return 是否包含
     */
    private static boolean shouldIncludeJvmArg(String arg) {
        // 排除一些可能导致问题的参数
        String[] excludePatterns = {
            "-agentlib:",           // 调试代理
            "-javaagent:",          // Java代理
            "-XX:+PrintGC",         // GC日志相关
            "-XX:+PrintGCDetails",
            "-Xloggc:",
            "-XX:+HeapDumpOnOutOfMemoryError", // 可能导致路径问题
            "-XX:HeapDumpPath=",
            "-Dcom.sun.management.jmxremote", // JMX远程管理
            "-Djava.awt.headless=true" // 某些情况下可能需要重新设置
        };

        for (String pattern : excludePatterns) {
            if (arg.startsWith(pattern)) {
                return false;
            }
        }

        return true;
    }

    /**
     * 使用当前JVM参数重启程序
     * @param mainClass 主类
     * @param programArgs 程序参数
     * @return Process对象
     */
    public static Process restartWithCurrentJvmArgs(Class<?> mainClass, String... programArgs) {
        List<String> jvmArgs = getFilteredJvmArgs();
        return restartWithJvmArgs(mainClass, jvmArgs.toArray(new String[0]), programArgs);
    }

    /**
     * 获取JVM信息的详细报告
     * @return JVM信息字符串
     */
    public static String getJvmInfo() {
        RuntimeMXBean runtimeMXBean = ManagementFactory.getRuntimeMXBean();
        StringBuilder info = new StringBuilder();

        info.append("=== JVM 信息 ===\n");
        info.append("JVM名称: ").append(runtimeMXBean.getVmName()).append("\n");
        info.append("JVM版本: ").append(runtimeMXBean.getVmVersion()).append("\n");
        info.append("JVM供应商: ").append(runtimeMXBean.getVmVendor()).append("\n");
        info.append("启动时间: ").append(new java.util.Date(runtimeMXBean.getStartTime())).append("\n");
        info.append("运行时间: ").append(runtimeMXBean.getUptime()).append(" ms\n");
        info.append("进程ID: ").append(runtimeMXBean.getName().split("@")[0]).append("\n");

        info.append("\n=== JVM 启动参数 ===\n");
        List<String> jvmArgs = runtimeMXBean.getInputArguments();
        if (jvmArgs.isEmpty()) {
            info.append("无JVM参数\n");
        } else {
            for (int i = 0; i < jvmArgs.size(); i++) {
                info.append(String.format("[%d] %s\n", i, jvmArgs.get(i)));
            }
        }

        info.append("\n=== 系统属性 ===\n");
        info.append("Java版本: ").append(System.getProperty("java.version")).append("\n");
        info.append("Java主目录: ").append(System.getProperty("java.home")).append("\n");
        info.append("类路径: ").append(System.getProperty("java.class.path")).append("\n");
        info.append("工作目录: ").append(System.getProperty("user.dir")).append("\n");
        info.append("用户名: ").append(System.getProperty("user.name")).append("\n");
        info.append("操作系统: ").append(System.getProperty("os.name")).append(" ")
            .append(System.getProperty("os.version")).append("\n");

        return info.toString();
    }

    // ========== 程序参数管理 ==========

    /**
     * 初始化程序参数（应在main方法开始时调用）
     * @param args 程序启动参数
     */
    public static void initProgramArgs(String[] args) {
        originalProgramArgs = args != null ? args.clone() : new String[0];
        cacheProgramArgs(originalProgramArgs);
    }

    /**
     * 获取原始程序参数
     * @return 程序启动时的参数数组
     */
    public static String[] getOriginalProgramArgs() {
        return originalProgramArgs.clone();
    }

    /**
     * 获取程序参数的副本（List形式）
     * @return 程序参数列表
     */
    public static List<String> getProgramArgsList() {
        return new ArrayList<>(Arrays.asList(originalProgramArgs));
    }

    /**
     * 缓存程序参数为键值对形式
     */
    private static void cacheProgramArgs(String[] args) {
        programArgsCache.clear();

        for (int i = 0; i < args.length; i++) {
            String arg = args[i];

            if (arg.startsWith("--")) {
                String key = arg.substring(2);
                if (i + 1 < args.length && !args[i + 1].startsWith("-")) {
                    // 有值的长选项
                    programArgsCache.put(key, args[++i]);
                } else {
                    // 标志选项
                    programArgsCache.put(key, "true");
                }
            } else if (arg.startsWith("-")) {
                String key = arg.substring(1);
                if (i + 1 < args.length && !args[i + 1].startsWith("-")) {
                    // 有值的短选项
                    programArgsCache.put(key, args[++i]);
                } else {
                    // 标志选项
                    programArgsCache.put(key, "true");
                }
            }
        }

        argsCached = true;
    }

    /**
     * 获取程序参数值
     * @param key 参数名（不包含前缀 - 或 --）
     * @return 参数值，如果不存在返回null
     */
    public static String getProgramArg(String key) {
        if (!argsCached) {
            cacheProgramArgs(originalProgramArgs);
        }
        return programArgsCache.get(key);
    }

    /**
     * 获取程序参数值，带默认值
     * @param key 参数名
     * @param defaultValue 默认值
     * @return 参数值或默认值
     */
    public static String getProgramArg(String key, String defaultValue) {
        String value = getProgramArg(key);
        return value != null ? value : defaultValue;
    }

    /**
     * 检查是否存在某个程序参数
     * @param key 参数名
     * @return 是否存在
     */
    public static boolean hasProgramArg(String key) {
        return getProgramArg(key) != null;
    }

    /**
     * 获取所有程序参数的键值对
     * @return 参数映射
     */
    public static Map<String, String> getAllProgramArgs() {
        if (!argsCached) {
            cacheProgramArgs(originalProgramArgs);
        }
        return new ConcurrentHashMap<>(programArgsCache);
    }

    /**
     * 使用原始程序参数重启
     * @param mainClass 主类
     * @return Process对象
     */
    public static Process restartWithOriginalArgs(Class<?> mainClass) {
        return restartWithExtraArgs(mainClass, originalProgramArgs);
    }

    /**
     * 使用原始程序参数和额外参数重启
     * @param mainClass 主类
     * @param extraArgs 额外参数
     * @return Process对象
     */
    public static Process restartWithOriginalAndExtraArgs(Class<?> mainClass, String... extraArgs) {
        List<String> allArgs = new ArrayList<>(Arrays.asList(originalProgramArgs));
        if (extraArgs != null) {
            allArgs.addAll(Arrays.asList(extraArgs));
        }
        return restartWithExtraArgs(mainClass, allArgs.toArray(new String[0]));
    }

    /**
     * 使用当前JVM参数和原始程序参数重启
     * @param mainClass 主类
     * @return Process对象
     */
    public static Process restartWithCurrentJvmAndOriginalArgs(Class<?> mainClass) {
        return restartWithCurrentJvmArgs(mainClass, originalProgramArgs);
    }

    /**
     * 获取程序参数的详细信息
     * @return 程序参数信息字符串
     */
    public static String getProgramArgsInfo() {
        StringBuilder info = new StringBuilder();
        info.append("=== 程序参数信息 ===\n");
        info.append("参数总数: ").append(originalProgramArgs.length).append("\n");

        if (originalProgramArgs.length > 0) {
            info.append("原始参数: ").append(Arrays.toString(originalProgramArgs)).append("\n");

            if (!argsCached) {
                cacheProgramArgs(originalProgramArgs);
            }

            info.append("解析后的参数:\n");
            for (Map.Entry<String, String> entry : programArgsCache.entrySet()) {
                info.append("  ").append(entry.getKey()).append(" = ").append(entry.getValue()).append("\n");
            }
        } else {
            info.append("无程序参数\n");
        }

        return info.toString();
    }

    /**
     * 从当前进程尝试获取命令行参数（实验性功能）
     * 注意：这个方法在某些平台和JVM版本上可能不工作
     * @return 命令行参数，如果无法获取则返回空数组
     */
    public static String[] tryGetCommandLineArgs() {
        try {
            RuntimeMXBean runtimeMXBean = ManagementFactory.getRuntimeMXBean();
            String jvmName = runtimeMXBean.getName();
            long pid = Long.parseLong(jvmName.split("@")[0]);

            // 尝试通过系统命令获取命令行参数
            return getCommandLineFromSystem(pid);
        } catch (Exception e) {
            System.err.println("无法获取命令行参数: " + e.getMessage());
            return new String[0];
        }
    }

    /**
     * 通过系统命令获取进程的命令行参数
     */
    private static String[] getCommandLineFromSystem(long pid) {
        try {
            String os = System.getProperty("os.name").toLowerCase();
            Process process;

            if (os.contains("win")) {
                // Windows: 使用wmic命令
                process = Runtime.getRuntime().exec(new String[]{
                    "wmic", "process", "where", "ProcessId=" + pid, "get", "CommandLine", "/format:list"
                });
            } else if (os.contains("mac") || os.contains("darwin")) {
                // macOS: 使用ps命令
                process = Runtime.getRuntime().exec(new String[]{
                    "ps", "-p", String.valueOf(pid), "-o", "command="
                });
            } else {
                // Linux: 读取/proc/pid/cmdline
                process = Runtime.getRuntime().exec(new String[]{
                    "cat", "/proc/" + pid + "/cmdline"
                });
            }

            java.io.BufferedReader reader = new java.io.BufferedReader(
                new java.io.InputStreamReader(process.getInputStream()));
            String line = reader.readLine();
            reader.close();

            if (line != null && !line.trim().isEmpty()) {
                // 解析命令行
                return parseCommandLine(line);
            }
        } catch (Exception e) {
            // 忽略异常，返回空数组
        }

        return new String[0];
    }

    /**
     * 解析命令行字符串为参数数组
     */
    private static String[] parseCommandLine(String commandLine) {
        // 简单的命令行解析，实际情况可能更复杂
        List<String> args = new ArrayList<>();
        boolean inQuotes = false;
        StringBuilder current = new StringBuilder();

        for (char c : commandLine.toCharArray()) {
            if (c == '"') {
                inQuotes = !inQuotes;
            } else if (c == ' ' && !inQuotes) {
                if (current.length() > 0) {
                    args.add(current.toString());
                    current.setLength(0);
                }
            } else {
                current.append(c);
            }
        }

        if (current.length() > 0) {
            args.add(current.toString());
        }

        return args.toArray(new String[0]);
    }


}
