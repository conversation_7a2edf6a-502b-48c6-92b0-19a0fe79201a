package com.mhome.env;

import java.io.File;
import java.util.Arrays;
import java.util.List;

public class ProcessUtils {

    public static Process start(String[] cmd){
        try {
            ProcessBuilder pb = new ProcessBuilder(cmd);
            pb.inheritIO();
            return pb.start();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    public static Process start(String[] cmd,File dir){
        try {
            ProcessBuilder pb = new ProcessBuilder(cmd);
            pb.directory(dir);
            pb.inheritIO();
            return pb.start();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static Process restart(Class<?> mainClass){
        String javaHome = System.getProperty("java.home");
        String javaBin = javaHome + File.separator + "bin" + File.separator + "java";
        String classpath = System.getProperty("java.class.path");
        String className = mainClass.getName();

        return start(new String[]{javaBin, "-cp", classpath, className});
    }

    public static Process restartWithExtraArgs(Class<?> mainClass,String... args){
        String javaHome = System.getProperty("java.home");
        String javaBin = javaHome + File.separator + "bin" + File.separator + "java";
        String classpath = System.getProperty("java.class.path");
        String className = mainClass.getName();

        List<String> list = Arrays.asList(new String[]{javaBin, "-cp", classpath, className});
        System.out.println(list);
        System.out.println(Arrays.asList(args));
        if(args!=null)list.addAll(Arrays.asList(args));

        

        return start(list.toArray(new String[]{}));
    }


}
