package com.mhome.sw;

public class Test {

    public static void main(String[] args) throws Exception {
        // 演示优雅的命令行参数解析
        demonstrateCommandLineParsing(args);

        System.out.println("start...");

        Runtime.getRuntime().addShutdownHook(new Thread() {
            @Override
            public void run() {
                System.out.println("shutdown...");
            }
        });

        Thread.currentThread().join();
    }

    /**
     * 演示最优雅的命令行参数解析方式
     */
    private static void demonstrateCommandLineParsing(String[] args) {
        System.out.println("=== 优雅的命令行参数解析演示 ===");

        // 如果没有参数，使用测试参数
        if (args.length == 0) {
            args = new String[]{
                "--host", "localhost",
                "--port", "8080",
                "--verbose",
                "--config", "app.properties",
                "file1.txt", "file2.txt"
            };
        }

        System.out.println("输入参数: " + java.util.Arrays.toString(args));

        // 方法1: 简单优雅的解析器
        parseWithSimpleElegantParser(args);

        // 方法2: 链式调用解析器
        parseWithChainedParser(args);

        System.out.println();
    }

    /**
     * 方法1: 简单优雅的解析器
     */
    private static void parseWithSimpleElegantParser(String[] args) {
        System.out.println("\n1. 简单优雅解析器:");

        CommandLineArgs parsed = CommandLineArgs.parse(args)
            .withDefault("host", "localhost")
            .withDefault("port", "8080")
            .withFlag("verbose")
            .build();

        System.out.println("  host: " + parsed.get("host"));
        System.out.println("  port: " + parsed.getInt("port"));
        System.out.println("  verbose: " + parsed.getBoolean("verbose"));
        System.out.println("  config: " + parsed.get("config"));
        System.out.println("  files: " + parsed.getFiles());
    }

    /**
     * 方法2: 链式调用解析器
     */
    private static void parseWithChainedParser(String[] args) {
        System.out.println("\n2. 链式调用解析器:");

        AppConfig config = new ArgParser()
            .option("--host").defaultValue("localhost")
            .option("--port").asInt().defaultValue(8080)
            .option("--verbose").asFlag()
            .option("--config")
            .parseToConfig(args);

        System.out.println("  " + config);
    }

    // ========== 简单优雅解析器实现 ==========

    static class CommandLineArgs {
        private final java.util.Map<String, String> values = new java.util.HashMap<>();
        private final java.util.Set<String> flags = new java.util.HashSet<>();
        private final java.util.List<String> files = new java.util.ArrayList<>();
        private final java.util.Map<String, String> defaults = new java.util.HashMap<>();

        private CommandLineArgs(String[] args) {
            parseArgs(args);
        }

        public static CommandLineArgs parse(String[] args) {
            return new CommandLineArgs(args);
        }

        public CommandLineArgs withDefault(String key, String value) {
            defaults.put(key, value);
            return this;
        }

        public CommandLineArgs withFlag(String key) {
            // 标记为标志选项
            return this;
        }

        public CommandLineArgs build() {
            return this;
        }

        private void parseArgs(String[] args) {
            for (int i = 0; i < args.length; i++) {
                String arg = args[i];

                if (arg.startsWith("--")) {
                    String key = arg.substring(2);

                    // 检查是否有值
                    if (i + 1 < args.length && !args[i + 1].startsWith("-")) {
                        values.put(key, args[++i]);
                    } else {
                        flags.add(key);
                    }
                } else if (!arg.startsWith("-")) {
                    files.add(arg);
                }
            }
        }

        public String get(String key) {
            return values.getOrDefault(key, defaults.get(key));
        }

        public int getInt(String key) {
            String value = get(key);
            return value != null ? Integer.parseInt(value) : 0;
        }

        public boolean getBoolean(String key) {
            return flags.contains(key);
        }

        public java.util.List<String> getFiles() {
            return files;
        }
    }

    // ========== 链式调用解析器实现 ==========

    static class ArgParser {
        private final java.util.Map<String, OptionConfig> options = new java.util.HashMap<>();
        private String currentOption;

        public ArgParser option(String name) {
            currentOption = name;
            options.put(name, new OptionConfig());
            return this;
        }

        public ArgParser defaultValue(Object value) {
            if (currentOption != null) {
                options.get(currentOption).defaultValue = value;
            }
            return this;
        }

        public ArgParser asInt() {
            if (currentOption != null) {
                options.get(currentOption).type = Integer.class;
            }
            return this;
        }

        public ArgParser asFlag() {
            if (currentOption != null) {
                options.get(currentOption).isFlag = true;
            }
            return this;
        }

        public AppConfig parseToConfig(String[] args) {
            AppConfig config = new AppConfig();

            // 设置默认值
            config.host = "localhost";
            config.port = 8080;
            config.verbose = false;

            // 解析参数
            for (int i = 0; i < args.length; i++) {
                String arg = args[i];
                OptionConfig optConfig = options.get(arg);

                if (optConfig != null) {
                    if (optConfig.isFlag) {
                        setConfigValue(config, arg, true);
                    } else if (i + 1 < args.length && !args[i + 1].startsWith("-")) {
                        String value = args[++i];
                        Object convertedValue = convertValue(value, optConfig.type);
                        setConfigValue(config, arg, convertedValue);
                    }
                }
            }

            return config;
        }

        private void setConfigValue(AppConfig config, String option, Object value) {
            switch (option) {
                case "--host": config.host = (String) value; break;
                case "--port": config.port = (Integer) value; break;
                case "--verbose": config.verbose = (Boolean) value; break;
                case "--config": config.configFile = (String) value; break;
            }
        }

        private Object convertValue(String value, Class<?> type) {
            if (type == Integer.class) return Integer.parseInt(value);
            if (type == Boolean.class) return Boolean.parseBoolean(value);
            return value;
        }
    }

    static class OptionConfig {
        Object defaultValue;
        Class<?> type = String.class;
        boolean isFlag = false;
    }

    static class AppConfig {
        String host;
        int port;
        boolean verbose;
        String configFile;

        @Override
        public String toString() {
            return String.format("AppConfig{host='%s', port=%d, verbose=%s, config='%s'}",
                               host, port, verbose, configFile);
        }
    }
}
