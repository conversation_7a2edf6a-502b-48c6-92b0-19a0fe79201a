package com.mhome.sw;

public class Test {

    public static void main(String[] args) throws Exception {
        // 演示优雅的命令行参数解析
        demonstrateCommandLineParsing(args);

        System.out.println("start...");

        Runtime.getRuntime().addShutdownHook(new Thread() {
            @Override
            public void run() {
                System.out.println("shutdown...");
            }
        });

        Thread.currentThread().join();
    }

    /**
     * 演示最优雅的命令行参数解析方式
     */
    private static void demonstrateCommandLineParsing(String[] args) {
        System.out.println("=== 优雅的命令行参数解析演示 ===");

        // 如果没有参数，使用测试参数
        if (args.length == 0) {
            // 测试多种复杂情况
            String[] testCases = {
                // 基本用法
                "--host localhost --port 8080 --verbose file1.txt file2.txt",
                // 短选项
                "-h localhost -p 8080 -v file1.txt file2.txt",
                // 等号格式
                "--host=************* --port=9000 file1.txt",
                // 混合格式
                "-v --host=localhost -p 8080 file1.txt file2.txt",
                // 使用 -- 分隔符
                "--host localhost -v -- --not-an-option file1.txt",
                // 只有位置参数
                "input.txt output.txt backup.txt"
            };

            System.out.println("=== 测试多种命令行参数格式 ===");
            for (int i = 0; i < testCases.length; i++) {
                System.out.println("\n--- 测试用例 " + (i + 1) + " ---");
                System.out.println("输入: " + testCases[i]);
                String[] testArgs = testCases[i].split("\\s+");
                demonstrateCommandLineParsing(testArgs);
            }
            return;
        }

        System.out.println("输入参数: " + java.util.Arrays.toString(args));

        // 方法1: 简单优雅的解析器
        parseWithSimpleElegantParser(args);

        // 方法2: 链式调用解析器
        parseWithChainedParser(args);

        System.out.println();
    }

    /**
     * 方法1: 简单优雅的解析器
     */
    private static void parseWithSimpleElegantParser(String[] args) {
        System.out.println("\n1. 简单优雅解析器:");

        CommandLineArgs parsed = CommandLineArgs.parse(args)
            .withDefault("host", "localhost")
            .withDefault("port", "8080")
            .withFlag("verbose")
            .build();

        System.out.println("  host: " + parsed.get("host"));
        System.out.println("  port: " + parsed.getInt("port"));
        System.out.println("  verbose: " + parsed.getBoolean("verbose"));
        System.out.println("  config: " + parsed.get("config"));
        System.out.println("  files: " + parsed.getFiles());
    }

    /**
     * 方法2: 链式调用解析器
     */
    private static void parseWithChainedParser(String[] args) {
        System.out.println("\n2. 链式调用解析器:");

        AppConfig config = new ArgParser()
            .option("--host", "-h").defaultValue("localhost")
            .option("--port", "-p").asInt().defaultValue(8080)
            .option("--verbose", "-v").asFlag()
            .option("--config", "-c")
            .parseToConfig(args);

        System.out.println("  " + config);
        System.out.println("  位置参数: " + config.files);
    }

    // ========== 简单优雅解析器实现 ==========

    static class CommandLineArgs {
        private final java.util.Map<String, String> values = new java.util.HashMap<>();
        private final java.util.Set<String> flags = new java.util.HashSet<>();
        private final java.util.List<String> positionalArgs = new java.util.ArrayList<>();
        private final java.util.Map<String, String> defaults = new java.util.HashMap<>();
        private final java.util.Set<String> flagOptions = new java.util.HashSet<>();

        private CommandLineArgs(String[] args) {
            parseArgs(args);
        }

        public static CommandLineArgs parse(String[] args) {
            return new CommandLineArgs(args);
        }

        public CommandLineArgs withDefault(String key, String value) {
            defaults.put(key, value);
            return this;
        }

        public CommandLineArgs withFlag(String key) {
            // 标记为标志选项，这样解析时就知道这个选项不需要值
            flagOptions.add(key);
            return this;
        }

        public CommandLineArgs build() {
            return this;
        }

        private void parseArgs(String[] args) {
            boolean endOfOptions = false;

            for (int i = 0; i < args.length; i++) {
                String arg = args[i];

                // 处理 -- 分隔符，之后的所有参数都视为位置参数
                if ("--".equals(arg)) {
                    endOfOptions = true;
                    continue;
                }

                if (!endOfOptions && arg.startsWith("--")) {
                    // 长选项 --option
                    String key = arg.substring(2);

                    // 处理 --option=value 格式
                    if (key.contains("=")) {
                        String[] parts = key.split("=", 2);
                        values.put(parts[0], parts[1]);
                    } else if (flagOptions.contains(key)) {
                        // 已知的标志选项
                        flags.add(key);
                    } else if (i + 1 < args.length && !args[i + 1].startsWith("-")) {
                        // 有值的选项
                        values.put(key, args[++i]);
                    } else {
                        // 没有值，当作标志处理
                        flags.add(key);
                    }
                } else if (!endOfOptions && arg.startsWith("-") && arg.length() > 1) {
                    // 短选项 -o 或组合短选项 -abc
                    String shortOpts = arg.substring(1);

                    for (int j = 0; j < shortOpts.length(); j++) {
                        String shortOpt = String.valueOf(shortOpts.charAt(j));

                        if (j == shortOpts.length() - 1 && i + 1 < args.length &&
                            !args[i + 1].startsWith("-") && !flagOptions.contains(shortOpt)) {
                            // 最后一个短选项且后面有值
                            values.put(shortOpt, args[++i]);
                        } else {
                            // 标志选项
                            flags.add(shortOpt);
                        }
                    }
                } else {
                    // 位置参数（不带 - 或 -- 的参数）
                    positionalArgs.add(arg);
                }
            }
        }

        public String get(String key) {
            return values.getOrDefault(key, defaults.get(key));
        }

        public int getInt(String key) {
            String value = get(key);
            return value != null ? Integer.parseInt(value) : 0;
        }

        public boolean getBoolean(String key) {
            return flags.contains(key);
        }

        public java.util.List<String> getFiles() {
            return positionalArgs;
        }

        public java.util.List<String> getPositionalArgs() {
            return positionalArgs;
        }

        public String getPositionalArg(int index) {
            return index < positionalArgs.size() ? positionalArgs.get(index) : null;
        }

        public int getPositionalArgCount() {
            return positionalArgs.size();
        }
    }

    // ========== 链式调用解析器实现 ==========

    static class ArgParser {
        private final java.util.Map<String, OptionConfig> options = new java.util.HashMap<>();
        private final java.util.Map<String, String> aliases = new java.util.HashMap<>();
        private String currentOption;

        public ArgParser option(String longName, String shortName) {
            currentOption = longName;
            options.put(longName, new OptionConfig());
            if (shortName != null) {
                aliases.put(shortName, longName);
            }
            return this;
        }

        public ArgParser option(String name) {
            return option(name, null);
        }

        public ArgParser defaultValue(Object value) {
            if (currentOption != null) {
                options.get(currentOption).defaultValue = value;
            }
            return this;
        }

        public ArgParser asInt() {
            if (currentOption != null) {
                options.get(currentOption).type = Integer.class;
            }
            return this;
        }

        public ArgParser asFlag() {
            if (currentOption != null) {
                options.get(currentOption).isFlag = true;
            }
            return this;
        }

        public AppConfig parseToConfig(String[] args) {
            AppConfig config = new AppConfig();

            // 设置默认值
            config.host = "localhost";
            config.port = 8080;
            config.verbose = false;
            config.files = new java.util.ArrayList<>();

            boolean endOfOptions = false;

            // 解析参数
            for (int i = 0; i < args.length; i++) {
                String arg = args[i];

                // 处理 -- 分隔符
                if ("--".equals(arg)) {
                    endOfOptions = true;
                    continue;
                }

                if (!endOfOptions && arg.startsWith("--")) {
                    // 长选项处理
                    i = parseLongOption(args, i, config);
                } else if (!endOfOptions && arg.startsWith("-") && arg.length() > 1) {
                    // 短选项处理
                    i = parseShortOptions(args, i, config);
                } else {
                    // 位置参数
                    config.files.add(arg);
                }
            }

            return config;
        }

        private int parseLongOption(String[] args, int index, AppConfig config) {
            String arg = args[index];
            String optionName;
            String optionValue = null;

            // 处理 --option=value 格式
            if (arg.contains("=")) {
                String[] parts = arg.split("=", 2);
                optionName = parts[0];
                optionValue = parts[1];
            } else {
                optionName = arg;
            }

            OptionConfig optConfig = options.get(optionName);
            if (optConfig == null) {
                System.err.println("未知选项: " + optionName);
                return index;
            }

            if (optConfig.isFlag) {
                setConfigValue(config, optionName, true);
            } else {
                if (optionValue != null) {
                    // 使用 = 后的值
                    Object convertedValue = convertValue(optionValue, optConfig.type);
                    setConfigValue(config, optionName, convertedValue);
                } else if (index + 1 < args.length && !args[index + 1].startsWith("-")) {
                    // 使用下一个参数作为值
                    String value = args[index + 1];
                    Object convertedValue = convertValue(value, optConfig.type);
                    setConfigValue(config, optionName, convertedValue);
                    return index + 1;
                } else {
                    System.err.println("选项 " + optionName + " 需要一个值");
                }
            }

            return index;
        }

        private int parseShortOptions(String[] args, int index, AppConfig config) {
            String arg = args[index];
            String shortOpts = arg.substring(1);

            for (int j = 0; j < shortOpts.length(); j++) {
                String shortOpt = "-" + shortOpts.charAt(j);
                String longName = aliases.get(shortOpt);

                if (longName == null) {
                    System.err.println("未知短选项: " + shortOpt);
                    continue;
                }

                OptionConfig optConfig = options.get(longName);

                if (optConfig.isFlag) {
                    setConfigValue(config, longName, true);
                } else if (j == shortOpts.length() - 1) {
                    // 最后一个短选项，可以有值
                    if (index + 1 < args.length && !args[index + 1].startsWith("-")) {
                        String value = args[index + 1];
                        Object convertedValue = convertValue(value, optConfig.type);
                        setConfigValue(config, longName, convertedValue);
                        return index + 1;
                    } else {
                        System.err.println("选项 " + shortOpt + " 需要一个值");
                    }
                } else {
                    System.err.println("带值的短选项 " + shortOpt + " 必须是最后一个");
                }
            }

            return index;
        }

        private void setConfigValue(AppConfig config, String option, Object value) {
            switch (option) {
                case "--host": config.host = (String) value; break;
                case "--port": config.port = (Integer) value; break;
                case "--verbose": config.verbose = (Boolean) value; break;
                case "--config": config.configFile = (String) value; break;
            }
        }

        private Object convertValue(String value, Class<?> type) {
            if (type == Integer.class) return Integer.parseInt(value);
            if (type == Boolean.class) return Boolean.parseBoolean(value);
            return value;
        }
    }

    static class OptionConfig {
        Object defaultValue;
        Class<?> type = String.class;
        boolean isFlag = false;
    }

    static class AppConfig {
        String host;
        int port;
        boolean verbose;
        String configFile;
        java.util.List<String> files;

        @Override
        public String toString() {
            return String.format("AppConfig{host='%s', port=%d, verbose=%s, config='%s', files=%s}",
                               host, port, verbose, configFile, files);
        }
    }
}
