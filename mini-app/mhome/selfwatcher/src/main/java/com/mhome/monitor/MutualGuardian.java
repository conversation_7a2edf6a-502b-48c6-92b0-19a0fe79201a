package com.mhome.monitor;

import java.io.*;
import java.lang.management.ManagementFactory;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 互相守护的Java程序
 * 主程序和守护程序合并为一个程序，互相守护对方一直运行
 */
public class MutualGuardian {
    
    // 配置常量
    private static final String MAIN_PROCESS_FLAG = "MAIN";
    private static final String DAEMON_PROCESS_FLAG = "DAEMON";
    private static final String PID_DIR = "pids";
    private static final String MAIN_PID_FILE = PID_DIR + "/main.pid";
    private static final String DAEMON_PID_FILE = PID_DIR + "/daemon.pid";
    private static final int HEARTBEAT_INTERVAL = 5; // 心跳间隔(秒)
    private static final int RESTART_DELAY = 3; // 重启延迟(秒)
    private static final int MAX_RESTART_ATTEMPTS = 5; // 最大重启尝试次数
    
    private final String processType;
    private final ScheduledExecutorService scheduler;
    private volatile boolean running = true;
    private int restartAttempts = 0;
    
    public MutualGuardian(String processType) {
        this.processType = processType;
        this.scheduler = Executors.newScheduledThreadPool(2);
    }
    
    public static void main(String[] args) {
        // 确定进程类型
        String processType = determineProcessType(args);
        
        System.out.println("=== 互相守护程序启动 ===");
        System.out.println("进程类型: " + processType);
        System.out.println("进程PID: " + getCurrentPid());
        System.out.println("启动时间: " + new java.util.Date());
        
        // 创建PID目录
        createPidDirectory();
        
        // 启动守护程序
        MutualGuardian guardian = new MutualGuardian(processType);
        guardian.start();
        
        // 添加关闭钩子
        Runtime.getRuntime().addShutdownHook(new Thread(guardian::shutdown));
        
        // 保持程序运行
        try {
            Thread.currentThread().join();
        } catch (InterruptedException e) {
            System.out.println("程序被中断，正在关闭...");
        }
    }
    
    /**
     * 确定进程类型
     */
    private static String determineProcessType(String[] args) {
        if (args.length > 0) {
            if (MAIN_PROCESS_FLAG.equals(args[0])) {
                return MAIN_PROCESS_FLAG;
            } else if (DAEMON_PROCESS_FLAG.equals(args[0])) {
                return DAEMON_PROCESS_FLAG;
            }
        }
        
        // 默认启动主进程，并自动启动守护进程
        if (args.length == 0) {
            startDaemonProcess();
            return MAIN_PROCESS_FLAG;
        }
        
        return MAIN_PROCESS_FLAG;
    }
    
    /**
     * 启动守护程序
     */
    public void start() {
        // 记录当前进程PID
        recordCurrentPid();
        
        // 启动心跳监控
        startHeartbeatMonitor();
        
        // 启动业务逻辑
        startBusinessLogic();
        
        System.out.println(processType + " 进程启动完成");
    }
    
    /**
     * 记录当前进程PID
     */
    private void recordCurrentPid() {
        try {
            String pid = getCurrentPid();
            String pidFile = MAIN_PROCESS_FLAG.equals(processType) ? MAIN_PID_FILE : DAEMON_PID_FILE;
            
            Files.write(Paths.get(pidFile), pid.getBytes());
            System.out.println("PID记录到文件: " + pidFile + " -> " + pid);
        } catch (IOException e) {
            System.err.println("记录PID失败: " + e.getMessage());
        }
    }
    
    /**
     * 启动心跳监控
     */
    private void startHeartbeatMonitor() {
        scheduler.scheduleWithFixedDelay(() -> {
            try {
                if (!running) return;
                
                // 检查对方进程是否存活
                boolean otherProcessAlive = checkOtherProcess();
                
                if (!otherProcessAlive) {
                    System.out.println("检测到对方进程已停止，准备重启...");
                    restartOtherProcess();
                } else {
                    // 重置重启尝试计数
                    restartAttempts = 0;
                }
                
                // 输出心跳信息
                System.out.println("[" + processType + "] 心跳检查 - " + 
                    new java.util.Date() + " - 对方进程状态: " + 
                    (otherProcessAlive ? "正常" : "异常"));
                
            } catch (Exception e) {
                System.err.println("心跳监控异常: " + e.getMessage());
            }
        }, HEARTBEAT_INTERVAL, HEARTBEAT_INTERVAL, TimeUnit.SECONDS);
    }
    
    /**
     * 启动业务逻辑
     */
    private void startBusinessLogic() {
        scheduler.scheduleWithFixedDelay(() -> {
            try {
                if (!running) return;
                
                if (MAIN_PROCESS_FLAG.equals(processType)) {
                    // 主进程业务逻辑
                    executeMainBusinessLogic();
                } else {
                    // 守护进程业务逻辑
                    executeDaemonBusinessLogic();
                }
                
            } catch (Exception e) {
                System.err.println("业务逻辑执行异常: " + e.getMessage());
            }
        }, 10, 30, TimeUnit.SECONDS); // 每30秒执行一次业务逻辑
    }
    
    /**
     * 主进程业务逻辑
     */
    private void executeMainBusinessLogic() {
        System.out.println("[主进程] 执行业务逻辑 - " + new java.util.Date());
        // 这里可以添加主进程的具体业务逻辑
        // 例如：处理用户请求、数据处理等
        
        // 模拟一些工作
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * 守护进程业务逻辑
     */
    private void executeDaemonBusinessLogic() {
        System.out.println("[守护进程] 执行监控任务 - " + new java.util.Date());
        // 这里可以添加守护进程的具体业务逻辑
        // 例如：系统监控、日志清理、健康检查等
        
        // 模拟监控任务
        try {
            // 检查系统资源
            Runtime runtime = Runtime.getRuntime();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;
            
            System.out.println("[守护进程] 内存使用情况 - 总计: " + 
                formatBytes(totalMemory) + ", 已用: " + formatBytes(usedMemory) + 
                ", 空闲: " + formatBytes(freeMemory));
                
        } catch (Exception e) {
            System.err.println("守护进程监控任务异常: " + e.getMessage());
        }
    }
    
    /**
     * 检查对方进程是否存活
     */
    private boolean checkOtherProcess() {
        try {
            String otherPidFile = MAIN_PROCESS_FLAG.equals(processType) ? DAEMON_PID_FILE : MAIN_PID_FILE;
            
            if (!Files.exists(Paths.get(otherPidFile))) {
                return false;
            }
            
            String otherPid = new String(Files.readAllBytes(Paths.get(otherPidFile))).trim();
            
            // 检查进程是否存在
            return isProcessAlive(otherPid);
            
        } catch (IOException e) {
            System.err.println("检查对方进程失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 重启对方进程
     */
    private void restartOtherProcess() {
        if (restartAttempts >= MAX_RESTART_ATTEMPTS) {
            System.err.println("重启尝试次数已达上限(" + MAX_RESTART_ATTEMPTS + ")，停止重启");
            return;
        }
        
        restartAttempts++;
        
        try {
            System.out.println("正在重启对方进程... (尝试次数: " + restartAttempts + ")");
            
            // 延迟重启
            Thread.sleep(RESTART_DELAY * 1000);
            
            String otherProcessType = MAIN_PROCESS_FLAG.equals(processType) ? DAEMON_PROCESS_FLAG : MAIN_PROCESS_FLAG;
            
            if (MAIN_PROCESS_FLAG.equals(processType)) {
                // 主进程重启守护进程
                startDaemonProcess();
            } else {
                // 守护进程重启主进程
                startMainProcess();
            }
            
            System.out.println("对方进程重启命令已发送");
            
        } catch (Exception e) {
            System.err.println("重启对方进程失败: " + e.getMessage());
        }
    }
    
    /**
     * 启动守护进程
     */
    private static void startDaemonProcess() {
        startProcess(DAEMON_PROCESS_FLAG);
    }
    
    /**
     * 启动主进程
     */
    private static void startMainProcess() {
        startProcess(MAIN_PROCESS_FLAG);
    }
    
    /**
     * 启动进程
     */
    private static void startProcess(String processType) {
        try {
            String javaHome = System.getProperty("java.home");
            String javaBin = javaHome + File.separator + "bin" + File.separator + "java";
            String classpath = System.getProperty("java.class.path");
            String className = MutualGuardian.class.getName();
            
            ProcessBuilder pb = new ProcessBuilder(
                javaBin,
                "-cp", classpath,
                className,
                processType
            );
            
            pb.inheritIO(); // 继承输入输出流
            Process process = pb.start();
            
            System.out.println("启动" + processType + "进程，PID: " + getProcessPid(process));
            
        } catch (IOException e) {
            System.err.println("启动进程失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取当前进程PID
     */
    private static String getCurrentPid() {
        return ManagementFactory.getRuntimeMXBean().getName().split("@")[0];
    }
    
    /**
     * 获取进程PID
     */
    private static String getProcessPid(Process process) {
        try {
            return String.valueOf(process.pid());
        } catch (Exception e) {
            return "unknown";
        }
    }
    
    /**
     * 检查进程是否存活
     */
    private static boolean isProcessAlive(String pid) {
        try {
            String os = System.getProperty("os.name").toLowerCase();
            Process process;
            
            if (os.contains("win")) {
                process = Runtime.getRuntime().exec("tasklist /FI \"PID eq " + pid + "\"");
            } else {
                process = Runtime.getRuntime().exec("kill -0 " + pid);
            }
            
            int exitCode = process.waitFor();
            return exitCode == 0;
            
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 创建PID目录
     */
    private static void createPidDirectory() {
        try {
            Files.createDirectories(Paths.get(PID_DIR));
        } catch (IOException e) {
            System.err.println("创建PID目录失败: " + e.getMessage());
        }
    }
    
    /**
     * 格式化字节数
     */
    private static String formatBytes(long bytes) {
        if (bytes < 1024) return bytes + " B";
        if (bytes < 1024 * 1024) return String.format("%.2f KB", bytes / 1024.0);
        if (bytes < 1024 * 1024 * 1024) return String.format("%.2f MB", bytes / (1024.0 * 1024));
        return String.format("%.2f GB", bytes / (1024.0 * 1024 * 1024));
    }
    
    /**
     * 关闭程序
     */
    public void shutdown() {
        System.out.println(processType + " 进程正在关闭...");
        running = false;
        
        if (scheduler != null && !scheduler.isShutdown()) {
            scheduler.shutdown();
            try {
                if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        // 清理PID文件
        try {
            String pidFile = MAIN_PROCESS_FLAG.equals(processType) ? MAIN_PID_FILE : DAEMON_PID_FILE;
            Files.deleteIfExists(Paths.get(pidFile));
        } catch (IOException e) {
            System.err.println("清理PID文件失败: " + e.getMessage());
        }
        
        System.out.println(processType + " 进程已关闭");
    }
}
