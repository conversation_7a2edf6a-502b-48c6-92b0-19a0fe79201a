package com.mhome.monitor;

import java.net.*;
import java.util.*;

/**
 * IP地址获取工具类
 * 提供多种方式获取本机IP地址
 */
public class IPAddressUtils {
    
    public static void main(String[] args) {
        System.out.println("=== 本机IP地址获取演示 ===\n");
        
        // 1. 获取本机主机名和IP
        demonstrateBasicHostInfo();
        
        // 2. 获取所有网络接口
        demonstrateAllNetworkInterfaces();
        
        // 3. 获取首选IP地址
        demonstratePreferredIP();
        
        // 4. 获取外网IP地址
        demonstratePublicIP();
        
        // 5. JMX URL构建示例
        demonstrateJMXURLConstruction();
    }
    
    /**
     * 1. 基本主机信息
     */
    private static void demonstrateBasicHostInfo() {
        System.out.println("=== 1. 基本主机信息 ===");
        
        try {
            // 获取本机主机名
            String hostname = InetAddress.getLocalHost().getHostName();
            System.out.println("主机名: " + hostname);
            
            // 获取本机IP地址（可能是127.0.0.1）
            String hostAddress = InetAddress.getLocalHost().getHostAddress();
            System.out.println("主机地址: " + hostAddress);
            
            // 获取完整的主机信息
            InetAddress localHost = InetAddress.getLocalHost();
            System.out.println("完整信息: " + localHost);
            
        } catch (UnknownHostException e) {
            System.err.println("获取主机信息失败: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    /**
     * 2. 获取所有网络接口
     */
    private static void demonstrateAllNetworkInterfaces() {
        System.out.println("=== 2. 所有网络接口 ===");
        
        try {
            Enumeration<NetworkInterface> interfaces = NetworkInterface.getNetworkInterfaces();
            
            while (interfaces.hasMoreElements()) {
                NetworkInterface networkInterface = interfaces.nextElement();
                
                // 跳过回环接口和未启用的接口
                if (networkInterface.isLoopback() || !networkInterface.isUp()) {
                    continue;
                }
                
                System.out.println("网络接口: " + networkInterface.getName() + 
                                 " (" + networkInterface.getDisplayName() + ")");
                
                Enumeration<InetAddress> addresses = networkInterface.getInetAddresses();
                while (addresses.hasMoreElements()) {
                    InetAddress address = addresses.nextElement();
                    
                    // 只显示IPv4地址
                    if (address instanceof Inet4Address) {
                        System.out.println("  IPv4地址: " + address.getHostAddress());
                    } else if (address instanceof Inet6Address) {
                        System.out.println("  IPv6地址: " + address.getHostAddress());
                    }
                }
                System.out.println();
            }
            
        } catch (SocketException e) {
            System.err.println("获取网络接口失败: " + e.getMessage());
        }
    }
    
    /**
     * 3. 获取首选IP地址
     */
    private static void demonstratePreferredIP() {
        System.out.println("=== 3. 首选IP地址 ===");
        
        // 方法1: 获取第一个非回环IPv4地址
        String firstNonLoopbackIP = getFirstNonLoopbackIPv4();
        System.out.println("第一个非回环IPv4地址: " + firstNonLoopbackIP);
        
        // 方法2: 通过连接外部地址获取本机IP
        String localIPByConnection = getLocalIPByConnection();
        System.out.println("通过连接获取的本机IP: " + localIPByConnection);
        
        // 方法3: 获取最佳IP地址
        String bestIP = getBestLocalIP();
        System.out.println("最佳本机IP地址: " + bestIP);
        
        System.out.println();
    }
    
    /**
     * 4. 获取外网IP地址（需要网络连接）
     */
    private static void demonstratePublicIP() {
        System.out.println("=== 4. 外网IP地址 ===");
        
        String publicIP = getPublicIP();
        System.out.println("外网IP地址: " + publicIP);
        
        System.out.println();
    }
    
    /**
     * 5. JMX URL构建示例
     */
    private static void demonstrateJMXURLConstruction() {
        System.out.println("=== 5. JMX URL构建示例 ===");
        
        int jmxPort = 9010;
        
        // 使用不同的IP地址构建JMX URL
        String[] ipAddresses = {
            "localhost",
            "127.0.0.1",
            "0.0.0.0",
            getFirstNonLoopbackIPv4(),
            getBestLocalIP()
        };
        
        for (String ip : ipAddresses) {
            if (ip != null) {
                String jmxURL = "service:jmx:rmi:///jndi/rmi://" + ip + ":" + jmxPort + "/jmxrmi";
                System.out.println("JMX URL (" + ip + "): " + jmxURL);
            }
        }
        
        System.out.println();
    }
    
    /**
     * 获取第一个非回环IPv4地址
     */
    public static String getFirstNonLoopbackIPv4() {
        try {
            Enumeration<NetworkInterface> interfaces = NetworkInterface.getNetworkInterfaces();
            
            while (interfaces.hasMoreElements()) {
                NetworkInterface networkInterface = interfaces.nextElement();
                
                // 跳过回环接口和未启用的接口
                if (networkInterface.isLoopback() || !networkInterface.isUp()) {
                    continue;
                }
                
                Enumeration<InetAddress> addresses = networkInterface.getInetAddresses();
                while (addresses.hasMoreElements()) {
                    InetAddress address = addresses.nextElement();
                    
                    // 返回第一个IPv4地址
                    if (address instanceof Inet4Address && !address.isLoopbackAddress()) {
                        return address.getHostAddress();
                    }
                }
            }
        } catch (SocketException e) {
            System.err.println("获取网络接口失败: " + e.getMessage());
        }
        
        return null;
    }
    
    /**
     * 通过连接外部地址获取本机IP
     */
    public static String getLocalIPByConnection() {
        try (Socket socket = new Socket()) {
            // 连接到外部地址（不会实际发送数据）
            socket.connect(new InetSocketAddress("www.baidu.com", 80));
            return socket.getLocalAddress().getHostAddress();
        } catch (Exception e) {
            System.err.println("通过连接获取IP失败: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 获取最佳本机IP地址
     */
    public static String getBestLocalIP() {
        try {
            // 首先尝试通过连接获取
            String ipByConnection = getLocalIPByConnection();
            if (ipByConnection != null && !ipByConnection.equals("127.0.0.1")) {
                return ipByConnection;
            }
            
            // 如果失败，获取第一个非回环地址
            String firstNonLoopback = getFirstNonLoopbackIPv4();
            if (firstNonLoopback != null) {
                return firstNonLoopback;
            }
            
            // 最后使用localhost
            return InetAddress.getLocalHost().getHostAddress();
            
        } catch (Exception e) {
            System.err.println("获取最佳IP失败: " + e.getMessage());
            return "127.0.0.1";
        }
    }
    
    /**
     * 获取所有本机IPv4地址
     */
    public static List<String> getAllLocalIPv4Addresses() {
        List<String> ipList = new ArrayList<>();
        
        try {
            Enumeration<NetworkInterface> interfaces = NetworkInterface.getNetworkInterfaces();
            
            while (interfaces.hasMoreElements()) {
                NetworkInterface networkInterface = interfaces.nextElement();
                
                // 跳过回环接口和未启用的接口
                if (networkInterface.isLoopback() || !networkInterface.isUp()) {
                    continue;
                }
                
                Enumeration<InetAddress> addresses = networkInterface.getInetAddresses();
                while (addresses.hasMoreElements()) {
                    InetAddress address = addresses.nextElement();
                    
                    if (address instanceof Inet4Address && !address.isLoopbackAddress()) {
                        ipList.add(address.getHostAddress());
                    }
                }
            }
        } catch (SocketException e) {
            System.err.println("获取所有IP地址失败: " + e.getMessage());
        }
        
        return ipList;
    }
    
    /**
     * 获取外网IP地址（通过HTTP服务）
     */
    public static String getPublicIP() {
        try {
            // 使用多个服务尝试获取外网IP
            String[] services = {
                "http://checkip.amazonaws.com",
                "http://icanhazip.com",
                "http://ipinfo.io/ip"
            };
            
            for (String service : services) {
                try {
                    URL url = new URL(service);
                    java.io.BufferedReader reader = new java.io.BufferedReader(
                        new java.io.InputStreamReader(url.openStream()));
                    String ip = reader.readLine().trim();
                    reader.close();
                    
                    // 验证IP格式
                    if (isValidIPv4(ip)) {
                        return ip;
                    }
                } catch (Exception e) {
                    // 尝试下一个服务
                    continue;
                }
            }
        } catch (Exception e) {
            System.err.println("获取外网IP失败: " + e.getMessage());
        }
        
        return "无法获取外网IP";
    }
    
    /**
     * 验证IPv4地址格式
     */
    public static boolean isValidIPv4(String ip) {
        if (ip == null || ip.isEmpty()) {
            return false;
        }
        
        String[] parts = ip.split("\\.");
        if (parts.length != 4) {
            return false;
        }
        
        try {
            for (String part : parts) {
                int num = Integer.parseInt(part);
                if (num < 0 || num > 255) {
                    return false;
                }
            }
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }
    
    /**
     * 构建JMX服务URL
     */
    public static String buildJMXServiceURL(String host, int port) {
        if (host == null || host.isEmpty()) {
            host = getBestLocalIP();
        }
        return "service:jmx:rmi:///jndi/rmi://" + host + ":" + port + "/jmxrmi";
    }
    
    /**
     * 构建JMX服务URL（使用最佳本机IP）
     */
    public static String buildJMXServiceURL(int port) {
        return buildJMXServiceURL(getBestLocalIP(), port);
    }
}
