package com.mhome.monitor;

import java.lang.management.ManagementFactory;

public class MonitorUtil {

    /**
     * 获取当前进程的PID
     * @return 当前进程的PID字符串
     */
    public static String getCurrentPid() {
        try {
            // 方法1：使用ManagementFactory (Java 9之前的兼容方式)
            String jvmName = ManagementFactory.getRuntimeMXBean().getName();
            // jvmName格式通常是 "pid@hostname"
            String pid = jvmName.split("@")[0];
            return pid;
        } catch (Exception e) {
            // 如果上述方法失败，尝试其他方式
            return getCurrentPidAlternative();
        }
    }

    /**
     * 根据进程名称获取进程PID
     * @param processName 进程名称
     * @return 进程PID字符串，如果未找到返回null
     */
    public static String getPid(String processName) {
        if (processName == null || processName.trim().isEmpty()) {
            return null;
        }

        try {
            String os = System.getProperty("os.name").toLowerCase();
            Process process;

            if (os.contains("win")) {
                // Windows系统
                process = Runtime.getRuntime().exec("tasklist /FI \"IMAGENAME eq " + processName + "\" /FO CSV");
            } else {
                // Unix/Linux/Mac系统
                process = Runtime.getRuntime().exec("pgrep -f " + processName);
            }

            java.io.BufferedReader reader = new java.io.BufferedReader(
                new java.io.InputStreamReader(process.getInputStream())
            );

            String line;
            if (os.contains("win")) {
                // Windows处理逻辑
                reader.readLine(); // 跳过标题行
                while ((line = reader.readLine()) != null) {
                    if (line.contains(processName)) {
                        String[] parts = line.split(",");
                        if (parts.length >= 2) {
                            return parts[1].replace("\"", "").trim();
                        }
                    }
                }
            } else {
                // Unix/Linux/Mac处理逻辑
                line = reader.readLine();
                if (line != null && !line.trim().isEmpty()) {
                    return line.trim();
                }
            }

            process.waitFor();
            reader.close();

        } catch (Exception e) {
            System.err.println("获取进程PID时发生错误: " + e.getMessage());
        }

        return null;
    }

    /**
     * 根据进程名称获取所有匹配的进程PID列表
     * @param processName 进程名称
     * @return 进程PID列表，如果未找到返回空列表
     */
    public static java.util.List<String> getAllPids(String processName) {
        java.util.List<String> pids = new java.util.ArrayList<>();

        if (processName == null || processName.trim().isEmpty()) {
            return pids;
        }

        try {
            String os = System.getProperty("os.name").toLowerCase();
            Process process;

            if (os.contains("win")) {
                // Windows系统
                process = Runtime.getRuntime().exec("tasklist /FI \"IMAGENAME eq " + processName + "\" /FO CSV");
            } else {
                // Unix/Linux/Mac系统
                process = Runtime.getRuntime().exec("pgrep -f " + processName);
            }

            java.io.BufferedReader reader = new java.io.BufferedReader(
                new java.io.InputStreamReader(process.getInputStream())
            );

            String line;
            if (os.contains("win")) {
                // Windows处理逻辑
                reader.readLine(); // 跳过标题行
                while ((line = reader.readLine()) != null) {
                    if (line.contains(processName)) {
                        String[] parts = line.split(",");
                        if (parts.length >= 2) {
                            pids.add(parts[1].replace("\"", "").trim());
                        }
                    }
                }
            } else {
                // Unix/Linux/Mac处理逻辑
                while ((line = reader.readLine()) != null) {
                    if (!line.trim().isEmpty()) {
                        pids.add(line.trim());
                    }
                }
            }

            process.waitFor();
            reader.close();

        } catch (Exception e) {
            System.err.println("获取进程PID列表时发生错误: " + e.getMessage());
        }

        return pids;
    }

    /**
     * 获取当前进程PID的备用方法
     * @return 当前进程的PID字符串，如果获取失败返回"unknown"
     */
    private static String getCurrentPidAlternative() {
        try {
            // 方法2：使用ProcessHandle (Java 9+) - 通过反射安全调用
            if (isJava9OrLater()) {
                try {
                    Class<?> processHandleClass = Class.forName("java.lang.ProcessHandle");
                    Object currentProcess = processHandleClass.getMethod("current").invoke(null);
                    long pid = (Long) processHandleClass.getMethod("pid").invoke(currentProcess);
                    return String.valueOf(pid);
                } catch (Exception reflectionException) {
                    // 反射调用失败，继续尝试其他方法
                }
            }

            // 方法3：通过系统属性尝试获取
            String pid = System.getProperty("PID");
            if (pid != null && !pid.isEmpty()) {
                return pid;
            }

            return "unknown";
        } catch (Exception e) {
            return "unknown";
        }
    }

    /**
     * 获取当前JVM进程的启动命令行参数
     * @return 命令行参数列表
     */
    public static java.util.List<String> getCurrentProcessArguments() {
        java.util.List<String> arguments = new java.util.ArrayList<>();

        try {
            // 方法1：使用ManagementFactory获取JVM参数
            java.lang.management.RuntimeMXBean runtimeBean = ManagementFactory.getRuntimeMXBean();

            // 获取JVM输入参数
            java.util.List<String> jvmArgs = runtimeBean.getInputArguments();
            arguments.addAll(jvmArgs);

            // 获取主类名
            String mainClass = System.getProperty("sun.java.command");
            if (mainClass != null) {
                arguments.add("MainClass: " + mainClass);
            }

            // 获取类路径
            String classPath = runtimeBean.getClassPath();
            if (classPath != null) {
                arguments.add("ClassPath: " + classPath);
            }

            // 获取库路径
            String libraryPath = runtimeBean.getLibraryPath();
            if (libraryPath != null) {
                arguments.add("LibraryPath: " + libraryPath);
            }

        } catch (Exception e) {
            arguments.add("Error getting arguments: " + e.getMessage());
        }

        return arguments;
    }

    /**
     * 获取指定进程的命令行参数
     * @param pid 进程ID
     * @return 命令行参数字符串，如果获取失败返回null
     */
    public static String getProcessArguments(String pid) {
        if (pid == null || pid.trim().isEmpty()) {
            return null;
        }

        try {
            String os = System.getProperty("os.name").toLowerCase();
            Process process;

            if (os.contains("win")) {
                // Windows系统 - 使用wmic获取命令行
                process = Runtime.getRuntime().exec("wmic process where processid=" + pid + " get commandline /format:list");
            } else if (os.contains("mac")) {
                // macOS系统 - 使用ps获取命令行
                process = Runtime.getRuntime().exec("ps -p " + pid + " -o command=");
            } else {
                // Linux系统 - 读取/proc/pid/cmdline
                process = Runtime.getRuntime().exec("cat /proc/" + pid + "/cmdline");
            }

            java.io.BufferedReader reader = new java.io.BufferedReader(
                new java.io.InputStreamReader(process.getInputStream())
            );

            StringBuilder result = new StringBuilder();
            String line;

            if (os.contains("win")) {
                // Windows处理逻辑
                while ((line = reader.readLine()) != null) {
                    if (line.startsWith("CommandLine=")) {
                        result.append(line.substring("CommandLine=".length()));
                        break;
                    }
                }
            } else if (os.contains("linux")) {
                // Linux处理逻辑 - /proc/pid/cmdline使用null分隔参数
                while ((line = reader.readLine()) != null) {
                    result.append(line.replace('\0', ' '));
                }
            } else {
                // macOS和其他Unix系统
                while ((line = reader.readLine()) != null) {
                    result.append(line).append(" ");
                }
            }

            process.waitFor();
            reader.close();

            return result.toString().trim();

        } catch (Exception e) {
            System.err.println("获取进程命令行参数时发生错误: " + e.getMessage());
            return null;
        }
    }

    /**
     * 获取当前进程的完整启动信息
     * @return 包含PID、命令行参数等信息的字符串
     */
    public static String getCurrentProcessInfo() {
        StringBuilder info = new StringBuilder();

        // 获取当前PID
        String pid = getCurrentPid();
        info.append("当前进程PID: ").append(pid).append("\n");

        // 获取JVM参数
        java.util.List<String> args = getCurrentProcessArguments();
        info.append("JVM启动参数:\n");
        for (String arg : args) {
            info.append("  ").append(arg).append("\n");
        }

        // 尝试获取完整命令行
        String cmdLine = getProcessArguments(pid);
        if (cmdLine != null && !cmdLine.isEmpty()) {
            info.append("完整命令行: ").append(cmdLine).append("\n");
        }

        return info.toString();
    }

    /**
     * 检查是否为Java 9或更高版本
     * @return true如果是Java 9+，否则false
     */
    private static boolean isJava9OrLater() {
        try {
            // 尝试访问ProcessHandle类，如果存在说明是Java 9+
            Class.forName("java.lang.ProcessHandle");
            return true;
        } catch (ClassNotFoundException e) {
            return false;
        }
    }
}
