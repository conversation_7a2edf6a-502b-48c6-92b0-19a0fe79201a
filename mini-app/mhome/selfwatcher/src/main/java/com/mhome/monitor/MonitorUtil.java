package com.mhome.monitor;

import java.lang.management.ManagementFactory;

public class MonitorUtil {

    /**
     * 获取当前进程的PID
     * @return 当前进程的PID字符串
     */
    public static String getCurrentPid() {
        try {
            // 方法1：使用ManagementFactory (Java 9之前的兼容方式)
            String jvmName = ManagementFactory.getRuntimeMXBean().getName();
            // jvmName格式通常是 "pid@hostname"
            String pid = jvmName.split("@")[0];
            return pid;
        } catch (Exception e) {
            // 如果上述方法失败，尝试其他方式
            return getCurrentPidAlternative();
        }
    }

    public static String getPid(String processName) {
        try {
            // 方法1：使用ManagementFactory (Java 9之前的兼容方式)
            String jvmName = ManagementFactory.getRuntimeMXBean().getName();
            // jvmName格式通常是 "pid@hostname"
            String pid = jvmName.split("@")[0];
            return pid;
        } catch (Exception e) {
            // 如果上述方法失败，尝试其他方式
            return getCurrentPidAlternative();
        }
    }

    /**
     * 获取当前进程PID的备用方法
     * @return 当前进程的PID字符串，如果获取失败返回"unknown"
     */
    private static String getCurrentPidAlternative() {
        try {
            // 方法2：使用ProcessHandle (Java 9+) - 通过反射安全调用
            if (isJava9OrLater()) {
                try {
                    Class<?> processHandleClass = Class.forName("java.lang.ProcessHandle");
                    Object currentProcess = processHandleClass.getMethod("current").invoke(null);
                    long pid = (Long) processHandleClass.getMethod("pid").invoke(currentProcess);
                    return String.valueOf(pid);
                } catch (Exception reflectionException) {
                    // 反射调用失败，继续尝试其他方法
                }
            }

            // 方法3：通过系统属性尝试获取
            String pid = System.getProperty("PID");
            if (pid != null && !pid.isEmpty()) {
                return pid;
            }

            return "unknown";
        } catch (Exception e) {
            return "unknown";
        }
    }

    /**
     * 检查是否为Java 9或更高版本
     * @return true如果是Java 9+，否则false
     */
    private static boolean isJava9OrLater() {
        try {
            // 尝试访问ProcessHandle类，如果存在说明是Java 9+
            Class.forName("java.lang.ProcessHandle");
            return true;
        } catch (ClassNotFoundException e) {
            return false;
        }
    }
}
