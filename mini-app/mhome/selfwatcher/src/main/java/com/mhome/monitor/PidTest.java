package com.mhome.monitor;

import java.util.List;

/**
 * 测试获取进程PID的功能
 */
public class PidTest {

    public static void main(String[] args) {
        System.out.println("=== 进程PID获取测试 ===");

        // 测试1: 获取当前进程PID
        testCurrentPid();

        // 测试2: 获取指定进程PID
        testSpecificProcess();

        System.out.println("=== 测试完成 ===");
    }

    private static void testCurrentPid() {
        System.out.println("\n--- 当前进程PID测试 ---");

        // 获取当前进程PID
        String pid = MonitorUtil.getCurrentPid();
        System.out.println("当前进程PID: " + pid);

        // 显示Java版本信息
        String javaVersion = System.getProperty("java.version");
        System.out.println("Java版本: " + javaVersion);

        // 显示JVM名称（通常包含PID信息）
        String jvmName = java.lang.management.ManagementFactory.getRuntimeMXBean().getName();
        System.out.println("JVM名称: " + jvmName);

        // 验证PID是否为数字
        try {
            Long.parseLong(pid);
            System.out.println("PID格式验证: ✓ 有效的数字格式");
        } catch (NumberFormatException e) {
            System.out.println("PID格式验证: ✗ 无效的数字格式");
        }
    }

    private static void testSpecificProcess() {
        System.out.println("\n--- 指定进程PID测试 ---");

        // 测试常见的进程名称
        String[] testProcesses = {"java", "chrome", "firefox", "code", "Terminal"};

        for (String processName : testProcesses) {
            System.out.println("\n查找进程: " + processName);

            // 获取单个PID
            String pid = MonitorUtil.getPid(processName);
            if (pid != null) {
                System.out.println("  找到PID: " + pid);
            } else {
                System.out.println("  未找到进程");
            }

            // 获取所有匹配的PID
            List<String> allPids = MonitorUtil.getAllPids(processName);
            if (!allPids.isEmpty()) {
                System.out.println("  所有匹配的PID: " + allPids);
                System.out.println("  进程数量: " + allPids.size());
            } else {
                System.out.println("  未找到任何匹配的进程");
            }
        }

        // 测试边界情况
        System.out.println("\n--- 边界情况测试 ---");
        System.out.println("空字符串测试: " + MonitorUtil.getPid(""));
        System.out.println("null测试: " + MonitorUtil.getPid(null));
        System.out.println("不存在的进程测试: " + MonitorUtil.getPid("nonexistent_process_12345"));
    }
}
