package com.mhome.monitor;

/**
 * 测试获取当前进程PID的功能
 */
public class PidTest {
    
    public static void main(String[] args) {
        System.out.println("=== 当前进程PID测试 ===");
        
        // 获取当前进程PID
        String pid = MonitorUtil.getCurrentPid();
        System.out.println("当前进程PID: " + pid);
        
        // 显示Java版本信息
        String javaVersion = System.getProperty("java.version");
        System.out.println("Java版本: " + javaVersion);
        
        // 显示JVM名称（通常包含PID信息）
        String jvmName = java.lang.management.ManagementFactory.getRuntimeMXBean().getName();
        System.out.println("JVM名称: " + jvmName);
        
        // 验证PID是否为数字
        try {
            Long.parseLong(pid);
            System.out.println("PID格式验证: ✓ 有效的数字格式");
        } catch (NumberFormatException e) {
            System.out.println("PID格式验证: ✗ 无效的数字格式");
        }
        
        System.out.println("=== 测试完成 ===");
    }
}
