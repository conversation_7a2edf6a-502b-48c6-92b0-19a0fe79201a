package com.mhome.monitor;

import java.util.*;

/**
 * 通用命令行参数解析器
 * 不依赖特定的配置类，返回通用的解析结果
 */
public class UniversalCommandLineParser {
    
    public static void main(String[] args) {
        System.out.println("=== 通用命令行参数解析器演示 ===\n");
        
        // 如果没有参数，运行测试用例
        if (args.length == 0) {
            runTestCases();
            return;
        }
        
        // 解析实际参数
        demonstrateUniversalParsing(args);
    }
    
    /**
     * 运行测试用例
     */
    private static void runTestCases() {
        String[] testCases = {
            "--host localhost --port 8080 --verbose file1.txt file2.txt",
            "-h ************* -p 9000 -vd file1.txt file2.txt",
            "--host=localhost --port=8080 --config=/path/to/config input.txt",
            "-v --host=localhost -p 8080 file1.txt file2.txt",
            "--host localhost -v -- --not-an-option file.txt",
            "input.txt output.txt backup.txt"
        };
        
        for (int i = 0; i < testCases.length; i++) {
            System.out.println("=== 测试用例 " + (i + 1) + " ===");
            System.out.println("输入: " + testCases[i]);
            String[] testArgs = testCases[i].split("\\s+");
            demonstrateUniversalParsing(testArgs);
            System.out.println();
        }
    }
    
    /**
     * 演示通用解析
     */
    private static void demonstrateUniversalParsing(String[] args) {
        try {
            // 创建通用解析器
            UniversalArgs result = UniversalArgsParser.create()
                .option("--host", "-h").withValue().defaultValue("localhost").description("服务器主机")
                .option("--port", "-p").withValue().asInt().defaultValue(8080).description("服务器端口")
                .option("--verbose", "-v").flag().description("启用详细输出")
                .option("--debug", "-d").flag().description("启用调试模式")
                .option("--config", "-c").withValue().description("配置文件路径")
                .option("--threads", "-t").withValue().asInt().defaultValue(1).description("线程数")
                .option("--timeout").withValue().asDouble().defaultValue(30.0).description("超时时间(秒)")
                .option("--help").flag().description("显示帮助信息")
                .parse(args);
            
            // 显示解析结果
            System.out.println("解析结果:");
            System.out.println("  所有选项: " + result.getAllOptions());
            System.out.println("  host: " + result.getString("host"));
            System.out.println("  port: " + result.getInt("port"));
            System.out.println("  verbose: " + result.getBoolean("verbose"));
            System.out.println("  debug: " + result.getBoolean("debug"));
            System.out.println("  config: " + result.getString("config"));
            System.out.println("  threads: " + result.getInt("threads"));
            System.out.println("  timeout: " + result.getDouble("timeout"));
            System.out.println("  位置参数: " + result.getPositionalArgs());
            System.out.println("  参数数量: " + result.getPositionalArgCount());
            
            // 演示动态访问
            System.out.println("\n动态访问:");
            for (String optionName : result.getOptionNames()) {
                Object value = result.getValue(optionName);
                System.out.println("  " + optionName + " = " + value + " (" + value.getClass().getSimpleName() + ")");
            }
            
        } catch (Exception e) {
            System.err.println("解析错误: " + e.getMessage());
        }
    }
    
    /**
     * 通用命令行参数解析器
     */
    static class UniversalArgsParser {
        private final Map<String, OptionDefinition> options = new HashMap<>();
        private final Map<String, String> aliases = new HashMap<>();
        
        public static UniversalArgsParser create() {
            return new UniversalArgsParser();
        }
        
        public OptionBuilder option(String longName, String shortName) {
            OptionDefinition def = new OptionDefinition(longName, shortName);
            options.put(longName, def);
            if (shortName != null) {
                aliases.put(shortName, longName);
            }
            return new OptionBuilder(this, def);
        }
        
        public OptionBuilder option(String longName) {
            return option(longName, null);
        }
        
        public UniversalArgs parse(String[] args) throws ParseException {
            Map<String, Object> values = new HashMap<>();
            List<String> positionalArgs = new ArrayList<>();
            boolean endOfOptions = false;
            
            // 设置默认值
            for (OptionDefinition def : options.values()) {
                String key = def.longName.replaceFirst("--", "");
                if (def.defaultValue != null) {
                    values.put(key, def.defaultValue);
                } else if (def.isFlag) {
                    values.put(key, false);
                }
            }
            
            for (int i = 0; i < args.length; i++) {
                String arg = args[i];
                
                // 处理 -- 分隔符
                if ("--".equals(arg)) {
                    endOfOptions = true;
                    continue;
                }
                
                if (!endOfOptions && arg.startsWith("--")) {
                    // 长选项
                    i = parseLongOption(args, i, values);
                } else if (!endOfOptions && arg.startsWith("-") && arg.length() > 1) {
                    // 短选项
                    i = parseShortOptions(args, i, values);
                } else {
                    // 位置参数
                    positionalArgs.add(arg);
                }
            }
            
            return new UniversalArgs(values, positionalArgs);
        }
        
        private int parseLongOption(String[] args, int index, Map<String, Object> values) throws ParseException {
            String arg = args[index];
            String optionName;
            String optionValue = null;
            
            // 处理 --option=value 格式
            if (arg.contains("=")) {
                String[] parts = arg.split("=", 2);
                optionName = parts[0];
                optionValue = parts[1];
            } else {
                optionName = arg;
            }
            
            OptionDefinition def = options.get(optionName);
            if (def == null) {
                throw new ParseException("未知选项: " + optionName);
            }
            
            String key = def.longName.replaceFirst("--", "");
            
            if (def.isFlag) {
                values.put(key, true);
            } else if (def.hasValue) {
                if (optionValue != null) {
                    values.put(key, convertValue(optionValue, def.type));
                } else if (index + 1 < args.length && !args[index + 1].startsWith("-")) {
                    values.put(key, convertValue(args[index + 1], def.type));
                    return index + 1;
                } else {
                    throw new ParseException("选项 " + optionName + " 需要一个值");
                }
            }
            
            return index;
        }
        
        private int parseShortOptions(String[] args, int index, Map<String, Object> values) throws ParseException {
            String arg = args[index];
            String shortOpts = arg.substring(1);
            
            for (int j = 0; j < shortOpts.length(); j++) {
                String shortOpt = "-" + shortOpts.charAt(j);
                String longName = aliases.get(shortOpt);
                
                if (longName == null) {
                    throw new ParseException("未知短选项: " + shortOpt);
                }
                
                OptionDefinition def = options.get(longName);
                String key = def.longName.replaceFirst("--", "");
                
                if (def.isFlag) {
                    values.put(key, true);
                } else if (def.hasValue) {
                    if (j == shortOpts.length() - 1) {
                        // 最后一个短选项，可以有值
                        if (index + 1 < args.length && !args[index + 1].startsWith("-")) {
                            values.put(key, convertValue(args[index + 1], def.type));
                            return index + 1;
                        } else {
                            throw new ParseException("选项 " + shortOpt + " 需要一个值");
                        }
                    } else {
                        throw new ParseException("带值的短选项 " + shortOpt + " 必须是最后一个");
                    }
                }
            }
            
            return index;
        }
        
        private Object convertValue(String value, Class<?> type) throws ParseException {
            try {
                if (type == Integer.class) {
                    return Integer.parseInt(value);
                } else if (type == Double.class) {
                    return Double.parseDouble(value);
                } else if (type == Boolean.class) {
                    return Boolean.parseBoolean(value);
                } else if (type == Long.class) {
                    return Long.parseLong(value);
                } else if (type == Float.class) {
                    return Float.parseFloat(value);
                }
                return value;
            } catch (NumberFormatException e) {
                throw new ParseException("无法转换值 '" + value + "' 为 " + type.getSimpleName());
            }
        }
    }
    
    /**
     * 选项构建器
     */
    static class OptionBuilder {
        private final UniversalArgsParser parser;
        private final OptionDefinition definition;
        
        OptionBuilder(UniversalArgsParser parser, OptionDefinition definition) {
            this.parser = parser;
            this.definition = definition;
        }
        
        public OptionBuilder withValue() {
            definition.hasValue = true;
            return this;
        }
        
        public OptionBuilder flag() {
            definition.isFlag = true;
            return this;
        }
        
        public OptionBuilder asInt() {
            definition.type = Integer.class;
            return this;
        }
        
        public OptionBuilder asDouble() {
            definition.type = Double.class;
            return this;
        }
        
        public OptionBuilder asLong() {
            definition.type = Long.class;
            return this;
        }
        
        public OptionBuilder asFloat() {
            definition.type = Float.class;
            return this;
        }
        
        public OptionBuilder defaultValue(Object value) {
            definition.defaultValue = value;
            return this;
        }
        
        public OptionBuilder description(String desc) {
            definition.description = desc;
            return this;
        }
        
        public OptionBuilder option(String longName, String shortName) {
            return parser.option(longName, shortName);
        }
        
        public OptionBuilder option(String longName) {
            return parser.option(longName);
        }
        
        public UniversalArgs parse(String[] args) throws ParseException {
            return parser.parse(args);
        }
    }
    
    /**
     * 选项定义
     */
    static class OptionDefinition {
        String longName;
        String shortName;
        boolean hasValue = false;
        boolean isFlag = false;
        Class<?> type = String.class;
        Object defaultValue;
        String description;
        
        OptionDefinition(String longName, String shortName) {
            this.longName = longName;
            this.shortName = shortName;
        }
    }
    
    /**
     * 通用解析结果
     */
    static class UniversalArgs {
        private final Map<String, Object> values;
        private final List<String> positionalArgs;
        
        UniversalArgs(Map<String, Object> values, List<String> positionalArgs) {
            this.values = values;
            this.positionalArgs = positionalArgs;
        }
        
        // 类型安全的获取方法
        public String getString(String key) {
            Object value = values.get(key);
            return value != null ? value.toString() : null;
        }
        
        public int getInt(String key) {
            Object value = values.get(key);
            return value instanceof Number ? ((Number) value).intValue() : 0;
        }
        
        public double getDouble(String key) {
            Object value = values.get(key);
            return value instanceof Number ? ((Number) value).doubleValue() : 0.0;
        }
        
        public long getLong(String key) {
            Object value = values.get(key);
            return value instanceof Number ? ((Number) value).longValue() : 0L;
        }
        
        public float getFloat(String key) {
            Object value = values.get(key);
            return value instanceof Number ? ((Number) value).floatValue() : 0.0f;
        }
        
        public boolean getBoolean(String key) {
            Object value = values.get(key);
            return value instanceof Boolean ? (Boolean) value : false;
        }
        
        // 通用获取方法
        public Object getValue(String key) {
            return values.get(key);
        }
        
        public <T> T getValue(String key, Class<T> type) {
            Object value = values.get(key);
            return type.isInstance(value) ? type.cast(value) : null;
        }
        
        // 位置参数方法
        public List<String> getPositionalArgs() {
            return new ArrayList<>(positionalArgs);
        }
        
        public String getPositionalArg(int index) {
            return index < positionalArgs.size() ? positionalArgs.get(index) : null;
        }
        
        public int getPositionalArgCount() {
            return positionalArgs.size();
        }
        
        // 元数据方法
        public boolean hasOption(String key) {
            return values.containsKey(key);
        }
        
        public Set<String> getOptionNames() {
            return new HashSet<>(values.keySet());
        }
        
        public Map<String, Object> getAllOptions() {
            return new HashMap<>(values);
        }
        
        @Override
        public String toString() {
            return String.format("UniversalArgs{options=%s, positionalArgs=%s}", values, positionalArgs);
        }
    }
    
    /**
     * 解析异常
     */
    static class ParseException extends Exception {
        ParseException(String message) {
            super(message);
        }
    }
}
